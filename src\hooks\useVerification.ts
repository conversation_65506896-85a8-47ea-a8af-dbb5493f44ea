import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';
import { useAuth } from './useAuth';
import { useToast } from '@/hooks/use-toast';
import {
  VerificationDocument,
  VerificationDocumentUpload,
  LandlordVerificationForm,
  FraudReportForm,
  AdminVerificationReview,
  DocumentReview,
  VerificationStats,
  TrustIndicators,
  PropertySafetyCheck,
  VerifiedProfile
} from '@/types/verification';

export const useVerification = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [verificationDocuments, setVerificationDocuments] = useState<VerificationDocument[]>([]);
  const [verificationStatus, setVerificationStatus] = useState<string>('pending');
  
  // Simple cache for trust indicators to prevent repeated API calls
  const [trustIndicatorsCache] = useState(new Map<string, TrustIndicators | null>());

  // Fetch user's verification documents
  const fetchVerificationDocuments = useCallback(async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('verification_documents')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setVerificationDocuments(data || []);
    } catch (error) {
      console.error('Error fetching verification documents:', error);
      toast({
        title: "Error",
        description: "Failed to fetch verification documents",
        variant: "destructive",
      });
    }
  }, [user, toast]);

  // Upload verification document
  const uploadVerificationDocument = async (documentUpload: VerificationDocumentUpload) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please log in to upload documents",
        variant: "destructive",
      });
      return null;
    }

    setLoading(true);
    try {
      // Upload file to storage
      const fileExt = documentUpload.file.name.split('.').pop();
      const fileName = `${user.id}/${documentUpload.document_type}_${Date.now()}.${fileExt}`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('verification-documents')
        .upload(fileName, documentUpload.file);

      if (uploadError) throw uploadError;

      // Store the file path instead of generating a URL immediately
      // The URL will be generated as a signed URL when needed for viewing
      const filePath = fileName;

      // For debugging - log the file path
      console.log('Uploaded file path:', filePath);

      // Save document record with file path
      const { data, error } = await supabase
        .from('verification_documents')
        .insert({
          user_id: user.id,
          document_type: documentUpload.document_type,
          document_url: filePath, // Store file path, not URL
          document_name: documentUpload.file.name,
          file_size: documentUpload.file.size,
          mime_type: documentUpload.file.type,
          expiry_date: documentUpload.expiry_date || null,
        })
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Document uploaded",
        description: "Your verification document has been uploaded successfully",
      });

      await fetchVerificationDocuments();
      return data;
    } catch (error) {
      console.error('Error uploading document:', error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload document",
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Submit landlord verification
  const submitLandlordVerification = async (formData: LandlordVerificationForm) => {
    if (!user) return null;

    setLoading(true);
    try {
      // Update profile with verification info
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          national_id: formData.national_id,
          business_registration_number: formData.business_registration_number,
          tax_pin: formData.tax_pin,
          business_name: formData.business_name,
          business_address: formData.business_address,
          years_in_business: formData.years_in_business,
          verification_status: 'under_review',
          verification_submitted_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (profileError) throw profileError;

      // Upload documents
      for (const doc of formData.documents) {
        await uploadVerificationDocument(doc);
      }

      toast({
        title: "Verification submitted",
        description: "Your verification application has been submitted for review",
      });

      return true;
    } catch (error) {
      console.error('Error submitting verification:', error);
      toast({
        title: "Submission failed",
        description: error instanceof Error ? error.message : "Failed to submit verification",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Submit fraud report
  const submitFraudReport = async (reportData: FraudReportForm) => {
    setLoading(true);
    try {
      const evidenceUrls: string[] = [];

      // Upload evidence files if any
      if (reportData.evidence_files && reportData.evidence_files.length > 0) {
        for (const file of reportData.evidence_files) {
          const fileExt = file.name.split('.').pop();
          const fileName = `fraud_evidence/${Date.now()}_${Math.random().toString(36).substring(7)}.${fileExt}`;
          
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('verification-documents')
            .upload(fileName, file);

          if (uploadError) throw uploadError;

          const { data: { publicUrl } } = supabase.storage
            .from('verification-documents')
            .getPublicUrl(fileName);

          evidenceUrls.push(publicUrl);
        }
      }

      // Submit fraud report
      const { data, error } = await supabase
        .from('fraud_reports')
        .insert({
          reported_user_id: reportData.reported_user_id,
          reported_property_id: reportData.reported_property_id,
          reporter_email: reportData.reporter_email,
          reporter_phone: reportData.reporter_phone,
          report_type: reportData.report_type,
          description: reportData.description,
          evidence_urls: evidenceUrls.length > 0 ? evidenceUrls : null,
        })
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Report submitted",
        description: "Your fraud report has been submitted. We will investigate this matter.",
      });

      return data;
    } catch (error) {
      console.error('Error submitting fraud report:', error);
      toast({
        title: "Submission failed",
        description: error instanceof Error ? error.message : "Failed to submit fraud report",
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Get trust indicators for a landlord
  const getTrustIndicators = useCallback(async (landlordId: string): Promise<TrustIndicators | null> => {
    // Check cache first
    if (trustIndicatorsCache.has(landlordId)) {
      return trustIndicatorsCache.get(landlordId) || null;
    }

    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', landlordId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching profile for landlord', landlordId, ':', error);
        trustIndicatorsCache.set(landlordId, null);
        return null;
      }

      if (!profile) {
        console.warn('Profile not found for landlord', landlordId, '- showing as unverified');
        trustIndicatorsCache.set(landlordId, null);
        return null;
      }

      // Calculate verification badge level
      let badgeLevel: 'none' | 'basic' | 'premium' | 'gold' = 'none';
      const isVerified = profile.verification_status === 'verified';
      if (isVerified) {
        const trustScore = profile.trust_score || 0;
        if (trustScore >= 4.5) badgeLevel = 'gold';
        else if (trustScore >= 4.0) badgeLevel = 'premium';
        else badgeLevel = 'basic';
      }

      const yearsActive = profile.created_at
        ? Math.max(0, Math.floor((Date.now() - new Date(profile.created_at).getTime()) / (365.25 * 24 * 60 * 60 * 1000)))
        : 0;

      const trustScore = profile.trust_score || 0;

      const trustData = {
        is_verified: isVerified,
        verification_badge_level: badgeLevel,
        trust_score: trustScore,
        total_properties: profile.total_properties || 0,
        successful_rentals: profile.successful_rentals || 0,
        years_active: yearsActive,
        response_rate: 85, // This would come from actual data
        average_rating: trustScore,
      };

      // Cache the result
      trustIndicatorsCache.set(landlordId, trustData);
      return trustData;
    } catch (error) {
      console.error('Error getting trust indicators for landlord', landlordId, ':', error);
      trustIndicatorsCache.set(landlordId, null);
      return null;
    }
  }, [trustIndicatorsCache]);

  // Get property safety check
  const getPropertySafetyCheck = useCallback(async (propertyId: string): Promise<PropertySafetyCheck | null> => {
    try {
      // Get property and owner info
      const { data: property, error: propertyError } = await supabase
        .from('properties')
        .select(`
          *,
          profiles!properties_user_id_fkey (*)
        `)
        .eq('id', propertyId)
        .single();

      if (propertyError) throw propertyError;

      // Get property verification (may not exist)
      const { data: verification, error: verificationError } = await supabase
        .from('property_verification')
        .select('*')
        .eq('property_id', propertyId)
        .maybeSingle();

      // If there's an error other than "no rows", throw it
      if (verificationError && verificationError.code !== 'PGRST116') {
        throw verificationError;
      }

      // Get fraud reports count
      const { count: fraudReportsCount } = await supabase
        .from('fraud_reports')
        .select('*', { count: 'exact', head: true })
        .eq('reported_property_id', propertyId)
        .eq('status', 'open');

      const profile = property.profiles as Database['public']['Tables']['profiles']['Row'] | null;
      const ownerVerified = profile?.verification_status === 'verified';
      const ownershipVerified = verification?.ownership_verified || false;
      const locationVerified = verification?.location_verified || false;
      const photosVerified = verification?.photos_verified || false;

      // Calculate safety score (0-100)
      let safetyScore = 0;
      if (ownerVerified) safetyScore += 40;
      if (ownershipVerified) safetyScore += 25;
      if (locationVerified) safetyScore += 15;
      if (photosVerified) safetyScore += 10;
      if ((fraudReportsCount || 0) === 0) safetyScore += 10;

      const warnings: string[] = [];
      const recommendations: string[] = [];

      if (!ownerVerified) {
        warnings.push("Property owner is not verified");
        recommendations.push("Request verification documents from the landlord");
      }
      if (!ownershipVerified) {
        warnings.push("Property ownership not verified");
      }
      if ((fraudReportsCount || 0) > 0) {
        warnings.push(`${fraudReportsCount} fraud report(s) filed against this property`);
      }

      return {
        property_id: propertyId,
        owner_verified: ownerVerified,
        ownership_documents_verified: ownershipVerified,
        location_verified: locationVerified,
        photos_authentic: photosVerified,
        recent_activity: true, // This would be calculated from actual activity
        fraud_reports_count: fraudReportsCount || 0,
        safety_score: safetyScore,
        warnings,
        recommendations,
      };
    } catch (error) {
      console.error('Error getting property safety check:', error);
      return null;
    }
  }, []);

  useEffect(() => {
    if (user) {
      fetchVerificationDocuments();
    }
  }, [user, fetchVerificationDocuments]);

  return {
    loading,
    verificationDocuments,
    verificationStatus,
    uploadVerificationDocument,
    submitLandlordVerification,
    submitFraudReport,
    getTrustIndicators,
    getPropertySafetyCheck,
    fetchVerificationDocuments,
  };
};
