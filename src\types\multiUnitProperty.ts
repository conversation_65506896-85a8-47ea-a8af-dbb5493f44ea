import { Database } from '../integrations/supabase/types';

// Enhanced database types for multi-unit properties
export type Property = Database['public']['Tables']['properties']['Row'] & {
  is_multi_unit?: boolean;
  building_name?: string;
  total_units?: number;
  property_type?: 'single_unit' | 'multi_unit' | 'complex';
};

export type PropertyUnit = {
  id: string;
  property_id: string;
  unit_name?: string;
  room_type: string;
  bedrooms: number;
  bathrooms: number;
  area?: number;
  rent: number;
  deposit?: number;
  is_available: boolean;
  unit_amenities?: string[];
  unit_description?: string;
  floor_number?: number;
  unit_number?: string;
  created_at: string;
  updated_at: string;
};

export type UnitImage = {
  id: string;
  unit_id: string;
  image_url: string;
  is_primary: boolean;
  created_at: string;
};

export type UnitInquiry = {
  id: string;
  unit_id: string;
  user_id?: string;
  inquiry_type: 'viewing' | 'booking' | 'application';
  message?: string;
  contact_phone?: string;
  preferred_date?: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  created_at: string;
};

// Enhanced property types with units
export interface PropertyWithUnits extends Property {
  property_images: Database['public']['Tables']['property_images']['Row'][];
  profiles?: Database['public']['Tables']['profiles']['Row'];
  property_units?: PropertyUnitWithImages[];
}

export interface PropertyUnitWithImages extends PropertyUnit {
  unit_images: UnitImage[];
}

// Multi-unit property form data
export interface MultiUnitPropertyFormData {
  // Building-level information
  title: string;
  building_name?: string;
  description?: string;
  location: string;
  county: string;
  zone?: string;
  property_type: 'single_unit' | 'multi_unit';
  amenities: string[]; // Building-level amenities
  featured: boolean;
  latitude?: number;
  longitude?: number;
  formatted_address?: string;
  neighborhood?: string;
  city?: string;

  // Units information (for multi-unit properties)
  units: UnitFormData[];
}

export interface UnitFormData {
  unit_name?: string;
  room_type: string;
  bedrooms: number;
  bathrooms: number;
  area?: number;
  rent: number;
  deposit?: number;
  is_available: boolean;
  unit_amenities: string[];
  unit_description?: string;
  floor_number?: number;
  unit_number?: string;
  images?: File[];
}

// Enhanced search filters for multi-unit properties
export interface EnhancedSearchFilters {
  location?: string;
  county?: string;
  zone?: string;
  maxRent?: number;
  minRent?: number;
  bedrooms?: number;
  bathrooms?: number;
  minArea?: number;
  maxArea?: number;
  amenities?: string[];
  available?: boolean;
  featured?: boolean;
  room_types?: string[]; // Filter by multiple room types
  property_type?: 'single_unit' | 'multi_unit' | 'all';
  building_amenities?: string[]; // Filter by building-level amenities
}

// Room type configurations with quantity
export interface RoomTypeConfig {
  room_type: string;
  quantity: number;
  rent: number;
  bedrooms: number;
  bathrooms: number;
  area?: number;
  deposit?: number;
  unit_amenities: string[];
  description?: string;
}

// Enhanced room types with better descriptions
export const ENHANCED_ROOM_TYPES = [
  { value: 'single_room', label: 'Single Room', description: 'Shared toilet and bathroom' },
  { value: 'single_room_ensuite', label: 'Single Room Ensuite', description: 'Private toilet and bathroom' },
  { value: 'bedsitter', label: 'Bedsitter', description: 'Combined bedroom and sitting room with kitchenette' },
  { value: 'studio', label: 'Studio Apartment', description: 'Open plan living with separate bathroom' },
  { value: '1_bedroom', label: '1 Bedroom', description: 'Separate bedroom, living room, and kitchen' },
  { value: '2_bedroom', label: '2 Bedroom', description: 'Two bedrooms with living areas' },
  { value: '3_bedroom', label: '3 Bedroom', description: 'Three bedrooms with spacious living areas' },
  { value: '4_bedroom', label: '4 Bedroom', description: 'Four bedrooms, ideal for large families' },
  { value: '5_bedroom', label: '5+ Bedroom', description: 'Five or more bedrooms' },
  { value: 'duplex', label: 'Duplex', description: 'Two-story unit' },
  { value: 'penthouse', label: 'Penthouse', description: 'Top floor luxury unit' },
  { value: 'maisonette', label: 'Maisonette', description: 'Multi-level house' },
  { value: 'cottage', label: 'Cottage', description: 'Standalone small house' }
] as const;

export type EnhancedRoomType = typeof ENHANCED_ROOM_TYPES[number]['value'];

// Property type options
export const PROPERTY_TYPES = [
  { value: 'single_unit', label: 'Single Unit Property', description: 'One rental unit (house, apartment, etc.)' },
  { value: 'multi_unit', label: 'Multi-Unit Property', description: 'Multiple units in same building/complex' }
] as const;

// Unit status for better management
export enum UnitStatus {
  AVAILABLE = 'available',
  RENTED = 'rented',
  PENDING = 'pending',
  MAINTENANCE = 'maintenance',
  RESERVED = 'reserved'
}

// Building amenities vs unit amenities
export const BUILDING_AMENITIES = [
  'Security Guard',
  'CCTV Surveillance',
  'Gated Community',
  'Parking Space',
  'Generator Backup',
  'Water Backup',
  'Elevator',
  'Swimming Pool',
  'Gym/Fitness Center',
  'Playground',
  'Garden/Landscaping',
  'Shopping Center Nearby',
  'School Nearby',
  'Hospital Nearby',
  'Public Transport',
  'Waste Management',
  'Cleaning Services',
  'Reception/Concierge'
] as const;

export const UNIT_AMENITIES = [
  'Air Conditioning',
  'Heating',
  'Furnished',
  'Semi-Furnished',
  'Kitchen Appliances',
  'Internet/WiFi Ready',
  'Cable TV Ready',
  'Balcony',
  'Terrace',
  'Garden Access',
  'Storage Space',
  'Walk-in Closet',
  'En-suite Bathroom',
  'Bathtub',
  'Shower',
  'Kitchen Island',
  'Dishwasher',
  'Washing Machine',
  'Dryer'
] as const;

// Analytics types for landlords
export interface PropertyAnalytics {
  total_units: number;
  occupied_units: number;
  available_units: number;
  maintenance_units: number;
  total_monthly_income: number;
  occupancy_rate: number;
  unit_type_breakdown: {
    room_type: string;
    total: number;
    occupied: number;
    average_rent: number;
  }[];
  recent_inquiries: number;
  pending_applications: number;
}

// Inquiry management types
export interface InquiryWithDetails extends UnitInquiry {
  unit: PropertyUnitWithImages;
  property: Property;
  user_profile?: Database['public']['Tables']['profiles']['Row'];
}

export interface InquiryFormData {
  unit_id: string;
  inquiry_type: 'viewing' | 'booking' | 'application';
  message?: string;
  contact_phone?: string;
  preferred_date?: string;
}

// Bulk operations for landlords
export interface BulkUnitOperation {
  unit_ids: string[];
  operation: 'mark_available' | 'mark_unavailable' | 'update_rent' | 'delete';
  new_values?: {
    rent?: number;
    is_available?: boolean;
  };
}

// Unit comparison for tenants
export interface UnitComparison {
  units: PropertyUnitWithImages[];
  comparison_fields: ('rent' | 'area' | 'bedrooms' | 'bathrooms' | 'amenities')[];
}

export default {
  Property,
  PropertyUnit,
  UnitImage,
  UnitInquiry,
  PropertyWithUnits,
  PropertyUnitWithImages,
  MultiUnitPropertyFormData,
  UnitFormData,
  EnhancedSearchFilters,
  RoomTypeConfig,
  ENHANCED_ROOM_TYPES,
  PROPERTY_TYPES,
  UnitStatus,
  BUILDING_AMENITIES,
  UNIT_AMENITIES,
  PropertyAnalytics,
  InquiryWithDetails,
  InquiryFormData,
  BulkUnitOperation,
  UnitComparison
};
