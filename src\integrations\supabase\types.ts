export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          full_name: string | null
          phone: string | null
          user_role: string
          verification_status: string
          verification_submitted_at: string | null
          verification_completed_at: string | null
          verified_by: string | null
          national_id: string | null
          business_registration_number: string | null
          tax_pin: string | null
          phone_verified: boolean
          email_verified: boolean
          phone_verification_code: string | null
          phone_verification_expires_at: string | null
          business_name: string | null
          business_address: string | null
          years_in_business: number | null
          trust_score: number
          total_properties: number
          successful_rentals: number
          admin_notes: string | null
          is_flagged: boolean
          flagged_reason: string | null
          flagged_at: string | null
          flagged_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          full_name?: string | null
          phone?: string | null
          user_role?: string
          verification_status?: string
          verification_submitted_at?: string | null
          verification_completed_at?: string | null
          verified_by?: string | null
          national_id?: string | null
          business_registration_number?: string | null
          tax_pin?: string | null
          phone_verified?: boolean
          email_verified?: boolean
          phone_verification_code?: string | null
          phone_verification_expires_at?: string | null
          business_name?: string | null
          business_address?: string | null
          years_in_business?: number | null
          trust_score?: number
          total_properties?: number
          successful_rentals?: number
          admin_notes?: string | null
          is_flagged?: boolean
          flagged_reason?: string | null
          flagged_at?: string | null
          flagged_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string | null
          phone?: string | null
          user_role?: string
          verification_status?: string
          verification_submitted_at?: string | null
          verification_completed_at?: string | null
          verified_by?: string | null
          national_id?: string | null
          business_registration_number?: string | null
          tax_pin?: string | null
          phone_verified?: boolean
          email_verified?: boolean
          phone_verification_code?: string | null
          phone_verification_expires_at?: string | null
          business_name?: string | null
          business_address?: string | null
          years_in_business?: number | null
          trust_score?: number
          total_properties?: number
          successful_rentals?: number
          admin_notes?: string | null
          is_flagged?: boolean
          flagged_reason?: string | null
          flagged_at?: string | null
          flagged_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      properties: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          rent: number
          location: string
          county: string
          zone: string | null
          room_type: string | null
          bedrooms: number
          bathrooms: number
          area: number
          amenities: string[]
          available: boolean
          featured: boolean
          approval_status: string
          approved_by: string | null
          approved_at: string | null
          rejection_reason: string | null
          requires_verification: boolean
          latitude: number | null
          longitude: number | null
          formatted_address: string | null
          neighborhood: string | null
          city: string | null
          is_multi_unit: boolean | null
          building_name: string | null
          total_units: number | null
          property_type: string | null
          units: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          rent: number
          location: string
          county: string
          zone?: string | null
          room_type?: string | null
          bedrooms: number
          bathrooms: number
          area: number
          amenities?: string[]
          available?: boolean
          featured?: boolean
          latitude?: number | null
          longitude?: number | null
          formatted_address?: string | null
          neighborhood?: string | null
          city?: string | null
          is_multi_unit?: boolean | null
          building_name?: string | null
          total_units?: number | null
          property_type?: string | null
          units?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          rent?: number
          location?: string
          county?: string
          zone?: string | null
          room_type?: string | null
          bedrooms?: number
          bathrooms?: number
          area?: number
          amenities?: string[]
          available?: boolean
          featured?: boolean
          latitude?: number | null
          longitude?: number | null
          formatted_address?: string | null
          neighborhood?: string | null
          city?: string | null
          is_multi_unit?: boolean | null
          building_name?: string | null
          total_units?: number | null
          property_type?: string | null
          units?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "properties_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      property_units: {
        Row: {
          id: string
          property_id: string
          unit_name: string | null
          room_type: string
          bedrooms: number
          bathrooms: number
          area: number | null
          rent: number
          deposit: number | null
          is_available: boolean
          unit_amenities: string[] | null
          unit_description: string | null
          floor_number: number | null
          unit_number: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          property_id: string
          unit_name?: string | null
          room_type: string
          bedrooms: number
          bathrooms: number
          area?: number | null
          rent: number
          deposit?: number | null
          is_available?: boolean
          unit_amenities?: string[] | null
          unit_description?: string | null
          floor_number?: number | null
          unit_number?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          property_id?: string
          unit_name?: string | null
          room_type?: string
          bedrooms?: number
          bathrooms?: number
          area?: number | null
          rent?: number
          deposit?: number | null
          is_available?: boolean
          unit_amenities?: string[] | null
          unit_description?: string | null
          floor_number?: number | null
          unit_number?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "property_units_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          }
        ]
      }
      unit_images: {
        Row: {
          id: string
          unit_id: string
          image_url: string
          is_primary: boolean
          created_at: string
        }
        Insert: {
          id?: string
          unit_id: string
          image_url: string
          is_primary?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          unit_id?: string
          image_url?: string
          is_primary?: boolean
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "unit_images_unit_id_fkey"
            columns: ["unit_id"]
            isOneToOne: false
            referencedRelation: "property_units"
            referencedColumns: ["id"]
          }
        ]
      }
      unit_inquiries: {
        Row: {
          id: string
          unit_id: string
          user_id: string | null
          inquiry_type: string
          message: string | null
          contact_phone: string | null
          preferred_date: string | null
          status: string
          created_at: string
        }
        Insert: {
          id?: string
          unit_id: string
          user_id?: string | null
          inquiry_type?: string
          message?: string | null
          contact_phone?: string | null
          preferred_date?: string | null
          status?: string
          created_at?: string
        }
        Update: {
          id?: string
          unit_id?: string
          user_id?: string | null
          inquiry_type?: string
          message?: string | null
          contact_phone?: string | null
          preferred_date?: string | null
          status?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "unit_inquiries_unit_id_fkey"
            columns: ["unit_id"]
            isOneToOne: false
            referencedRelation: "property_units"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "unit_inquiries_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      property_images: {
        Row: {
          id: string
          property_id: string
          image_url: string
          is_primary: boolean
          created_at: string
        }
        Insert: {
          id?: string
          property_id: string
          image_url: string
          is_primary?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          property_id?: string
          image_url?: string
          is_primary?: boolean
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "property_images_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          }
        ]
      }
      favorites: {
        Row: {
          id: string
          user_id: string
          property_id: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          property_id: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          property_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "favorites_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "favorites_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      verification_documents: {
        Row: {
          id: string
          user_id: string
          document_type: string
          document_url: string
          document_name: string
          file_size: number | null
          mime_type: string | null
          verification_status: string
          reviewed_by: string | null
          reviewed_at: string | null
          rejection_reason: string | null
          expiry_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          document_type: string
          document_url: string
          document_name: string
          file_size?: number | null
          mime_type?: string | null
          verification_status?: string
          reviewed_by?: string | null
          reviewed_at?: string | null
          rejection_reason?: string | null
          expiry_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          document_type?: string
          document_url?: string
          document_name?: string
          file_size?: number | null
          mime_type?: string | null
          verification_status?: string
          reviewed_by?: string | null
          reviewed_at?: string | null
          rejection_reason?: string | null
          expiry_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      property_verification: {
        Row: {
          id: string
          property_id: string
          verification_status: string
          ownership_verified: boolean
          location_verified: boolean
          photos_verified: boolean
          verified_by: string | null
          verified_at: string | null
          rejection_reason: string | null
          admin_notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          property_id: string
          verification_status?: string
          ownership_verified?: boolean
          location_verified?: boolean
          photos_verified?: boolean
          verified_by?: string | null
          verified_at?: string | null
          rejection_reason?: string | null
          admin_notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          property_id?: string
          verification_status?: string
          ownership_verified?: boolean
          location_verified?: boolean
          photos_verified?: boolean
          verified_by?: string | null
          verified_at?: string | null
          rejection_reason?: string | null
          admin_notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      fraud_reports: {
        Row: {
          id: string
          reported_user_id: string
          reported_property_id: string | null
          reporter_email: string
          reporter_phone: string | null
          report_type: string
          description: string
          evidence_urls: string[] | null
          status: string
          priority: string
          assigned_to: string | null
          resolution_notes: string | null
          resolved_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          reported_user_id: string
          reported_property_id?: string | null
          reporter_email: string
          reporter_phone?: string | null
          report_type: string
          description: string
          evidence_urls?: string[] | null
          status?: string
          priority?: string
          assigned_to?: string | null
          resolution_notes?: string | null
          resolved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          reported_user_id?: string
          reported_property_id?: string | null
          reporter_email?: string
          reporter_phone?: string | null
          report_type?: string
          description?: string
          evidence_urls?: string[] | null
          status?: string
          priority?: string
          assigned_to?: string | null
          resolution_notes?: string | null
          resolved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      verification_audit_log: {
        Row: {
          id: string
          user_id: string
          action: string
          details: Json | null
          performed_by: string | null
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          action: string
          details?: Json | null
          performed_by?: string | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          action?: string
          details?: Json | null
          performed_by?: string | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
