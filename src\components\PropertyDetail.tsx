import { Contact<PERSON>andlord } from './ContactLandlord';
import { ShareProperty } from './ShareProperty';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PropertyWithOwner } from "@/types/property";
import {
  MapPin,
  Bed,
  Bath,
  Square,
  Phone,
  Mail,
  Calendar,
  ArrowLeft,
  Star,
  Shield
} from "lucide-react";
import { useState, useEffect, memo } from "react";
import MapComponent from "./MapComponent";
import { TrustIndicators } from "./verification/TrustIndicators";
import { PropertySafetyCheck } from "./verification/PropertySafetyCheck";
import { FraudReportForm } from "./verification/FraudReportForm";
import { useVerification } from "@/hooks/useVerification";
import { TrustIndicators as TrustData } from "@/types/verification";

interface PropertyDetailProps {
  property: PropertyWithOwner;
  onBack: () => void;
}

const PropertyDetail = memo(({ property, onBack }: PropertyDetailProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { getTrustIndicators } = useVerification();
  const [trustData, setTrustData] = useState<TrustData | null>(null);
  const [loadingTrust, setLoadingTrust] = useState(true);
  const [hasLoadedTrustOnce, setHasLoadedTrustOnce] = useState(false);

  useEffect(() => {
    let isMounted = true;
    
    const loadTrustData = async () => {
      if (property.user_id && !hasLoadedTrustOnce) {
        setLoadingTrust(true);
        try {
          const data = await getTrustIndicators(property.user_id);
          if (isMounted) {
            setTrustData(data);
            setHasLoadedTrustOnce(true);
          }
        } catch (error) {
          console.error('Error loading trust data for property detail:', error);
          if (isMounted) {
            setTrustData(null);
            setHasLoadedTrustOnce(true);
          }
        } finally {
          if (isMounted) {
            setLoadingTrust(false);
          }
        }
      } else if (!property.user_id) {
        setLoadingTrust(false);
        setTrustData(null);
        setHasLoadedTrustOnce(true);
      }
    };

    loadTrustData();

    return () => {
      isMounted = false;
    };
  }, [property.user_id, getTrustIndicators, hasLoadedTrustOnce]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-KE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card border-b">
        <div className="container mx-auto px-4 py-4">
          <Button 
            variant="ghost" 
            onClick={onBack}
            className="mb-3 sm:mb-4"
            size="sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Back to Properties</span>
            <span className="sm:hidden">Back</span>
          </Button>
          
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div className="flex-1">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-2">{property.title}</h1>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-0 text-muted-foreground">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                  <span className="text-sm sm:text-base">{property.location}</span>
                </div>
                {property.featured && (
                  <Badge className="ml-0 sm:ml-3 bg-primary self-start">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                )}
              </div>

              {/* County and Zone */}
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline" className="text-sm">
                  📍 {property.county}
                </Badge>
                {property.zone && (
                  <Badge variant="secondary" className="text-sm">
                    🏘️ {property.zone}
                  </Badge>
                )}
              </div>
            </div>
            <div className="text-left lg:text-right w-full lg:w-auto">
              <div className="text-2xl sm:text-3xl font-bold text-primary">
                {formatPrice(property.rent)}
              </div>
              <div className="text-muted-foreground text-sm sm:text-base">per month</div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-4 sm:py-8">
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-4 sm:space-y-8">
            {/* Image Gallery */}
            <Card>
              <CardContent className="p-0">
                <div className="relative">
                  <img
                    src={property.property_images?.[currentImageIndex]?.image_url || "/placeholder.svg"}
                    alt={`${property.title} - Image ${currentImageIndex + 1}`}
                    className="w-full h-64 sm:h-80 lg:h-96 object-cover rounded-t-lg"
                  />
                  {property.property_images && property.property_images.length > 1 && (
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                      <div className="flex space-x-2">
                        {property.property_images.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentImageIndex(index)}
                            className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${
                              index === currentImageIndex 
                                ? 'bg-white' 
                                : 'bg-white/50'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                {property.property_images && property.property_images.length > 1 && (
                  <div className="p-3 sm:p-4">
                    <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
                      {property.property_images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentImageIndex(index)}
                          className={`relative aspect-square rounded-lg overflow-hidden ${
                            index === currentImageIndex 
                              ? 'ring-2 ring-primary' 
                              : ''
                          }`}
                        >
                          <img
                            src={image.image_url || "/placeholder.svg"}
                            alt={`Thumbnail ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Property Details */}
            <Card>
              <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl">Property Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                <div className="grid grid-cols-3 gap-2 sm:gap-4">
                  <div className="text-center p-3 sm:p-4 bg-muted rounded-lg">
                    <Bed className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                    <div className="font-semibold text-sm sm:text-base">{property.bedrooms}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Bedrooms</div>
                  </div>
                  <div className="text-center p-3 sm:p-4 bg-muted rounded-lg">
                    <Bath className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                    <div className="font-semibold text-sm sm:text-base">{property.bathrooms}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Bathrooms</div>
                  </div>
                  <div className="text-center p-3 sm:p-4 bg-muted rounded-lg">
                    <Square className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                    <div className="font-semibold text-sm sm:text-base">{property.area} sqft</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Area</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-sm sm:text-base">Description</h4>
                  <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                    {property.description}
                  </p>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-sm sm:text-base">Amenities</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                    {property.amenities?.map((amenity) => (
                      <Badge key={amenity} variant="secondary" className="justify-center text-xs">
                        <Shield className="h-3 w-3 mr-1" />
                        {amenity}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Map */}
            <Card>
              <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl">Location</CardTitle>
              </CardHeader>
              <CardContent className="p-4 sm:p-6">
                <MapComponent
                  latitude={property.latitude}
                  longitude={property.longitude}
                  location={property.location}
                  title={property.title}
                />
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-4 sm:space-y-6">
            {/* Landlord Trust Indicators */}
            {loadingTrust && !hasLoadedTrustOnce ? (
              <Card>
                <CardContent className="p-4">
                  <div className="animate-pulse space-y-3">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            ) : trustData ? (
              <TrustIndicators trustData={trustData} showDetailed={false} />
            ) : (
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                    <Shield className="h-4 w-4" />
                    <span className="text-sm">Landlord verification unavailable</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Property Safety Check */}
            <PropertySafetyCheck
              propertyId={property.id}
              landlordId={property.user_id}
              landlordName={property.profiles?.full_name}
              propertyTitle={property.title}
              showDetailed={false}
            />

            {/* Contact Landlord */}
            <ContactLandlord property={property} />

            {/* Share Property */}
            <ShareProperty property={property} />

            {/* Quick Actions */}
            <Card>
              <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl">Interested in this property?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 p-4 sm:p-6">
                <Button variant="outline" className="w-full text-sm" size="sm">
                  Share Property
                </Button>
                <FraudReportForm
                  reportedUserId={property.user_id}
                  reportedPropertyId={property.id}
                  landlordName={property.profiles?.full_name}
                  propertyTitle={property.title}
                  trigger={
                    <Button variant="outline" className="w-full text-sm text-red-600 border-red-200 hover:bg-red-50" size="sm">
                      Report Issue
                    </Button>
                  }
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
});

PropertyDetail.displayName = "PropertyDetail";

export default PropertyDetail;