import React, { useState } from "react";
import { PropertyWithUnits, PropertyUnitWithImages, UnitInquiry } from "@/types/multiUnitProperty";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  MapPin, 
  Bed, 
  Bath, 
  Square, 
  Phone, 
  Mail, 
  Calendar,
  ArrowLeft,
  Star,
  Shield,
  Building,
  Users,
  CheckCircle,
  XCircle,
  Clock,
  Filter
} from "lucide-react";
import MapComponent from "./MapComponent";

interface EnhancedPropertyDetailProps {
  property: PropertyWithUnits;
  onBack: () => void;
  onInquireUnit?: (unitId: string) => void;
}

const EnhancedPropertyDetail = ({ property, onBack, onInquireUnit }: EnhancedPropertyDetailProps) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState<{[key: string]: number}>({});
  const [selectedRoomType, setSelectedRoomType] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'rent' | 'area' | 'bedrooms'>('rent');

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-KE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get unique room types for filtering
  const uniqueRoomTypes = property.property_units 
    ? [...new Set(property.property_units.map(unit => unit.room_type))]
    : [];

  // Filter and sort units
  const filteredUnits = property.property_units
    ? property.property_units
        .filter(unit => selectedRoomType === 'all' || unit.room_type === selectedRoomType)
        .sort((a, b) => {
          switch (sortBy) {
            case 'rent': return a.rent - b.rent;
            case 'area': return (a.area || 0) - (b.area || 0);
            case 'bedrooms': return a.bedrooms - b.bedrooms;
            default: return 0;
          }
        })
    : [];

  // Calculate property statistics
  const totalUnits = property.property_units?.length || 1;
  const availableUnits = property.property_units?.filter(unit => unit.is_available).length || (property.available ? 1 : 0);
  const rentRange = property.property_units?.length 
    ? {
        min: Math.min(...property.property_units.map(unit => unit.rent)),
        max: Math.max(...property.property_units.map(unit => unit.rent))
      }
    : { min: property.rent, max: property.rent };

  const setImageIndex = (unitId: string, index: number) => {
    setSelectedImageIndex(prev => ({ ...prev, [unitId]: index }));
  };

  const getImageIndex = (unitId: string) => {
    return selectedImageIndex[unitId] || 0;
  };

  const renderUnitCard = (unit: PropertyUnitWithImages) => {
    const currentImageIndex = getImageIndex(unit.id);
    const images = unit.unit_images?.length > 0 ? unit.unit_images : property.property_images;

    return (
      <Card key={unit.id} className="overflow-hidden">
        {/* Unit Images */}
        <div className="relative">
          <img
            src={images?.[currentImageIndex]?.image_url || "/placeholder.svg"}
            alt={`${unit.unit_name || unit.room_type} - Image ${currentImageIndex + 1}`}
            className="w-full h-40 sm:h-48 object-cover"
          />
          
          {/* Availability Badge */}
          <div className="absolute top-2 right-2">
            {unit.is_available ? (
              <Badge className="bg-green-500 text-white text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Available
              </Badge>
            ) : (
              <Badge className="bg-red-500 text-white text-xs">
                <XCircle className="h-3 w-3 mr-1" />
                Occupied
              </Badge>
            )}
          </div>

          {/* Image Navigation */}
          {images && images.length > 1 && (
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2">
              <div className="flex space-x-1">
                {images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setImageIndex(unit.id, index)}
                    className={`w-2 h-2 rounded-full ${
                      index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        <CardContent className="p-3 sm:p-4">
          {/* Unit Header */}
          <div className="flex justify-between items-start mb-3">
            <div>
              <h4 className="font-semibold text-base sm:text-lg">
                {unit.unit_name || `${unit.room_type.replace('_', ' ')} Unit`}
              </h4>
              <p className="text-xs sm:text-sm text-muted-foreground capitalize">
                {unit.room_type.replace('_', ' ')}
              </p>
            </div>
            <div className="text-right">
              <div className="text-lg sm:text-xl font-bold text-primary">
                {formatPrice(unit.rent)}
              </div>
              <div className="text-xs text-muted-foreground">/month</div>
            </div>
          </div>

          {/* Unit Details */}
          <div className="grid grid-cols-3 gap-2 sm:gap-3 mb-3">
            <div className="text-center p-2 bg-muted rounded">
              <Bed className="h-4 w-4 mx-auto mb-1 text-primary" />
              <div className="text-xs sm:text-sm font-semibold">{unit.bedrooms}</div>
              <div className="text-xs text-muted-foreground">Bed</div>
            </div>
            <div className="text-center p-2 bg-muted rounded">
              <Bath className="h-4 w-4 mx-auto mb-1 text-primary" />
              <div className="text-xs sm:text-sm font-semibold">{unit.bathrooms}</div>
              <div className="text-xs text-muted-foreground">Bath</div>
            </div>
            <div className="text-center p-2 bg-muted rounded">
              <Square className="h-4 w-4 mx-auto mb-1 text-primary" />
              <div className="text-xs sm:text-sm font-semibold">{unit.area || 'N/A'}</div>
              <div className="text-xs text-muted-foreground">sqft</div>
            </div>
          </div>

          {/* Unit Description */}
          {unit.unit_description && (
            <p className="text-xs sm:text-sm text-muted-foreground mb-3 line-clamp-2">
              {unit.unit_description}
            </p>
          )}

          {/* Unit Amenities */}
          {unit.unit_amenities && unit.unit_amenities.length > 0 && (
            <div className="mb-3">
              <div className="flex flex-wrap gap-1">
                {unit.unit_amenities.slice(0, 2).map((amenity) => (
                  <Badge key={amenity} variant="secondary" className="text-xs">
                    {amenity}
                  </Badge>
                ))}
                {unit.unit_amenities.length > 2 && (
                  <Badge variant="secondary" className="text-xs">
                    +{unit.unit_amenities.length - 2} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Action Button */}
          <Button 
            className="w-full" 
            size="sm"
            disabled={!unit.is_available}
            onClick={() => onInquireUnit?.(unit.id)}
          >
            <span className="text-sm">{unit.is_available ? 'Inquire About Unit' : 'Unit Not Available'}</span>
          </Button>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card border-b">
        <div className="container mx-auto px-4 py-4">
          <Button 
            variant="ghost" 
            onClick={onBack}
            className="mb-3 sm:mb-4"
            size="sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Back to Properties</span>
            <span className="sm:hidden">Back</span>
          </Button>
          
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div className="flex-1">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-2">
                {property.building_name || property.title}
              </h1>
              
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-muted-foreground">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                  <span className="text-sm sm:text-base">{property.location}</span>
                </div>
                {property.featured && (
                  <Badge className="ml-0 sm:ml-3 bg-primary self-start">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                )}
                {property.is_multi_unit && (
                  <Badge variant="secondary" className="ml-0 sm:ml-3 self-start">
                    <Building className="h-3 w-3 mr-1" />
                    Multi-Unit Property
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="text-left lg:text-right w-full lg:w-auto">
              {property.is_multi_unit ? (
                <>
                  <div className="text-xl sm:text-2xl font-bold text-primary">
                    {formatPrice(rentRange.min)} - {formatPrice(rentRange.max)}
                  </div>
                  <div className="text-muted-foreground text-sm sm:text-base">
                    {availableUnits} of {totalUnits} units available
                  </div>
                </>
              ) : (
                <>
                  <div className="text-xl sm:text-2xl font-bold text-primary">
                    {formatPrice(property.rent)}
                  </div>
                  <div className="text-muted-foreground text-sm sm:text-base">per month</div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-4 sm:py-8">
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-4 sm:space-y-8">
            {property.is_multi_unit ? (
              <>
                {/* Building Overview */}
                <Card>
                  <CardHeader className="p-4 sm:p-6">
                    <CardTitle className="flex items-center text-lg sm:text-xl">
                      <Building className="h-5 w-5 mr-2" />
                      Building Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 sm:p-6">
                    <div className="grid grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6">
                      <div className="text-center p-3 sm:p-4 bg-muted rounded-lg">
                        <Users className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                        <div className="font-semibold text-sm sm:text-base">{totalUnits}</div>
                        <div className="text-xs sm:text-sm text-muted-foreground">Total Units</div>
                      </div>
                      <div className="text-center p-3 sm:p-4 bg-muted rounded-lg">
                        <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-green-500" />
                        <div className="font-semibold text-sm sm:text-base">{availableUnits}</div>
                        <div className="text-xs sm:text-sm text-muted-foreground">Available</div>
                      </div>
                      <div className="text-center p-3 sm:p-4 bg-muted rounded-lg">
                        <Clock className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-orange-500" />
                        <div className="font-semibold text-sm sm:text-base">{totalUnits - availableUnits}</div>
                        <div className="text-xs sm:text-sm text-muted-foreground">Occupied</div>
                      </div>
                    </div>
                    
                    {property.description && (
                      <div>
                        <h4 className="font-semibold mb-2 text-sm sm:text-base">Description</h4>
                        <p className="text-muted-foreground text-sm sm:text-base">{property.description}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Units Section */}
                <Card>
                  <CardHeader className="p-4 sm:p-6">
                    <CardTitle className="text-lg sm:text-xl">Available Units</CardTitle>
                    
                    {/* Filters */}
                    <div className="flex flex-col sm:flex-row gap-3">
                      <select
                        value={selectedRoomType}
                        onChange={(e) => setSelectedRoomType(e.target.value)}
                        className="px-3 py-2 border rounded-md text-sm w-full sm:w-auto"
                      >
                        <option value="all">All Room Types</option>
                        {uniqueRoomTypes.map(type => (
                          <option key={type} value={type}>
                            {type.replace('_', ' ').charAt(0).toUpperCase() + type.replace('_', ' ').slice(1)}
                          </option>
                        ))}
                      </select>
                      
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value as 'rent' | 'area' | 'bedrooms')}
                        className="px-3 py-2 border rounded-md text-sm w-full sm:w-auto"
                      >
                        <option value="rent">Sort by Rent</option>
                        <option value="area">Sort by Area</option>
                        <option value="bedrooms">Sort by Bedrooms</option>
                      </select>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="p-4 sm:p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                      {filteredUnits.map(unit => renderUnitCard(unit))}
                    </div>
                    
                    {filteredUnits.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        No units match your current filters.
                      </div>
                    )}
                  </CardContent>
                </Card>
              </>
            ) : (
              /* Single Unit Property - Original Layout */
              <>
                {/* Image Gallery */}
                <Card>
                  <CardContent className="p-0">
                    {/* Original single property image gallery code */}
                  </CardContent>
                </Card>

                {/* Property Details */}
                <Card>
                  <CardHeader className="p-4 sm:p-6">
                    <CardTitle className="text-lg sm:text-xl">Property Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                    <div className="grid grid-cols-3 gap-2 sm:gap-4">
                      <div className="text-center p-3 sm:p-4 bg-muted rounded-lg">
                        <Bed className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                        <div className="font-semibold text-sm sm:text-base">{property.bedrooms}</div>
                        <div className="text-xs sm:text-sm text-muted-foreground">Bedrooms</div>
                      </div>
                      <div className="text-center p-3 sm:p-4 bg-muted rounded-lg">
                        <Bath className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                        <div className="font-semibold text-sm sm:text-base">{property.bathrooms}</div>
                        <div className="text-xs sm:text-sm text-muted-foreground">Bathrooms</div>
                      </div>
                      <div className="text-center p-3 sm:p-4 bg-muted rounded-lg">
                        <Square className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                        <div className="font-semibold text-sm sm:text-base">{property.area} sqft</div>
                        <div className="text-xs sm:text-sm text-muted-foreground">Area</div>
                      </div>
                    </div>

                    {property.description && (
                      <div>
                        <h4 className="font-semibold mb-3 text-sm sm:text-base">Description</h4>
                        <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                          {property.description}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </>
            )}

            {/* Building Amenities */}
            {property.amenities && property.amenities.length > 0 && (
              <Card>
                <CardHeader className="p-4 sm:p-6">
                  <CardTitle className="text-lg sm:text-xl">
                    {property.is_multi_unit ? 'Building Amenities' : 'Amenities'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 sm:p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                    {property.amenities.map((amenity) => (
                      <Badge key={amenity} variant="secondary" className="justify-center text-xs">
                        <Shield className="h-3 w-3 mr-1" />
                        {amenity}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Map */}
            <Card>
              <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl">Location</CardTitle>
              </CardHeader>
              <CardContent className="p-4 sm:p-6">
                <MapComponent
                  latitude={property.latitude}
                  longitude={property.longitude}
                  location={property.location}
                  title={property.building_name || property.title}
                />
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-4 sm:space-y-6">
            {/* Contact Landlord */}
            <Card>
              <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl">Contact Property Owner</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 p-4 sm:p-6">
                <div>
                  <h4 className="font-semibold text-foreground text-sm sm:text-base">
                    {property.profiles?.full_name || "Property Owner"}
                  </h4>
                  <p className="text-xs sm:text-sm text-muted-foreground">Property Owner</p>
                </div>
                
                <div className="space-y-3">
                  <Button 
                    className="w-full" 
                    size="sm"
                    onClick={() => {
                      const phone = property.profiles?.phone || "+254 700 000 000";
                      window.open(`tel:${phone}`, '_self');
                    }}
                  >
                    <Phone className="h-4 w-4 mr-2" />
                    <span className="truncate text-sm">{property.profiles?.phone || "+254 700 000 000"}</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full" 
                    size="sm"
                    onClick={() => {
                      const subject = encodeURIComponent(`Inquiry about: ${property.building_name || property.title}`);
                      const body = encodeURIComponent(`Hi ${property.profiles?.full_name || "Property Owner"},\n\nI am interested in your property "${property.building_name || property.title}" located at ${property.location}.\n\nCould you please provide more details?\n\nThank you!`);
                      window.open(`mailto:?subject=${subject}&body=${body}`, '_self');
                    }}
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="text-sm">Send Email</span>
                  </Button>
                </div>

                <div className="text-center pt-4 border-t">
                  <div className="flex items-center justify-center text-xs sm:text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4 mr-1" />
                    Posted on {formatDate(property.created_at)}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl">Interested in this property?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 p-4 sm:p-6">
                <Button variant="outline" className="w-full text-sm" size="sm">
                  Share Property
                </Button>
                <Button variant="outline" className="w-full text-sm" size="sm">
                  Report Issue
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedPropertyDetail;
