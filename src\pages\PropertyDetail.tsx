import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Heart, 
  MapPin, 
  Bed, 
  Bath, 
  Square, 
  Star,
  Phone,
  Mail,
  Calendar,
  CheckCircle,
  X,
  Navigation as DirectionIcon,
  Map,
  Share2,
  CalendarCheck
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Navigation from "@/components/Navigation";
import PropertyCard from "@/components/PropertyCard";
import MapComponent from "@/components/MapComponent";
import { ShareProperty } from "@/components/ShareProperty";
import { ReportIssue } from "@/components/ReportIssue";
import { useProperties } from "@/hooks/useProperties";
import { useFavorites } from "@/hooks/useFavorites";
import { useSearch } from "@/hooks/useSearch";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { PropertyWithOwner, PropertyWithImages } from "@/types/property";
import { AspectRatio } from "@/components/ui/aspect-ratio";

const PropertyDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const { getPropertyById } = useProperties();
  const { toggleFavorite, isFavorite } = useFavorites();
  const { getSimilarProperties } = useSearch();
  
  const [property, setProperty] = useState<PropertyWithOwner | null>(null);
  const [similarProperties, setSimilarProperties] = useState<PropertyWithImages[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  useEffect(() => {
    const fetchProperty = async () => {
      if (!id) {
        console.error('No property ID provided');
        setLoading(false);
        return;
      }
      
      setLoading(true);
      try {
        const propertyData = await getPropertyById(id);
        
        if (propertyData) {
          setProperty(propertyData);
          
          // Fetch similar properties
          const similar = await getSimilarProperties(
            propertyData.id,
            propertyData.county,
            propertyData.rent,
            propertyData.bedrooms
          );
          setSimilarProperties(similar as PropertyWithImages[]);
        } else {
          console.error('Property not found');
        }
      } catch (error) {
        console.error('Error fetching property details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProperty();
  }, [id, getPropertyById, getSimilarProperties]);

  const handleToggleFavorite = () => {
    if (id) {
      toggleFavorite(id);
    }
  };

  const handleGetDirections = () => {
    if (property?.latitude && property?.longitude) {
      // Try Google Maps first (most popular)
      const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${property.latitude},${property.longitude}`;
      window.open(googleMapsUrl, '_blank');
    } else if (property?.location) {
      // Fallback to search by location name
      const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(property.location + ', ' + property.county)}`;
      window.open(googleMapsUrl, '_blank');
    } else {
      alert('Location coordinates not available for this property.');
    }
  };

  const handleViewOnMap = () => {
    if (property?.latitude && property?.longitude) {
      // Open location in Google Maps
      const googleMapsUrl = `https://www.google.com/maps?q=${property.latitude},${property.longitude}`;
      window.open(googleMapsUrl, '_blank');
    } else if (property?.location) {
      // Fallback to search by location name
      const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(property.location + ', ' + property.county)}`;
      window.open(googleMapsUrl, '_blank');
    }
  };

  const handleContactOwner = () => {
    console.log('handleContactOwner called', property?.profiles?.phone);
    if (property?.profiles?.phone) {
      try {
        // Create the tel: link
        const telLink = `tel:${property.profiles.phone}`;
        console.log('Opening phone dialer:', telLink);
        
        // Use window.location.href for better compatibility
        window.location.href = telLink;
      } catch (error) {
        console.error('Error opening phone dialer:', error);
        // Fallback: copy number to clipboard and show toast
        if (navigator.clipboard) {
          navigator.clipboard.writeText(property.profiles.phone).then(() => {
            toast({
              title: "Phone number copied",
              description: `${property.profiles.phone} has been copied to your clipboard`,
            });
          }).catch(() => {
            toast({
              title: "Call " + property.profiles.phone,
              description: "Please dial this number manually",
            });
          });
        } else {
          toast({
            title: "Call " + property.profiles.phone,
            description: "Please dial this number manually",
          });
        }
      }
    } else {
      console.log('No phone number available');
      toast({
        title: "No phone number",
        description: "Contact information is not available for this property",
        variant: "destructive",
      });
    }
  };

  const handleEmailOwner = () => {
    console.log('handleEmailOwner called', property?.title);
    if (property) {
      try {
        // Create a pre-filled email for the property inquiry
        const subject = encodeURIComponent(`Inquiry about: ${property.title}`);
        const body = encodeURIComponent(
          `Hi ${property.profiles?.full_name || 'Property Owner'},\n\n` +
          `I am interested in your property listing: ${property.title}\n` +
          `Location: ${property.location}, ${property.county}\n` +
          `Rent: KSh ${property.rent?.toLocaleString()}\n\n` +
          `I found this listing on BomaHub. Could you please provide more information?\n\n` +
          `Best regards`
        );
        
        // Open default email client with pre-filled content
        const mailtoLink = `mailto:?subject=${subject}&body=${body}`;
        console.log('Opening email client:', mailtoLink);
        
        // Use window.location.href for better compatibility
        window.location.href = mailtoLink;
      } catch (error) {
        console.error('Error opening email client:', error);
        toast({
          title: "Email client not available",
          description: "Please copy the property details and contact the owner directly",
          variant: "destructive",
        });
      }
    } else {
      console.log('No property data available');
      toast({
        title: "Error",
        description: "Property information is not available",
        variant: "destructive",
      });
    }
  };

  const handleSimilarPropertyClick = (propertyId: string) => {
    navigate(`/property/${propertyId}`);
  };

  const handleScheduleViewing = () => {
    if (!user) {
      toast({
        title: "Sign in required",
        description: "Please sign in to schedule a viewing.",
        variant: "destructive",
      });
      return;
    }
    
    if (property?.profiles?.phone) {
      const message = `Hi, I'm interested in scheduling a viewing for the property "${property.title}" in ${property.location} (Rent: KSH ${property.rent.toLocaleString()}/month). I found this listing on BomaHub. When would be a good time to visit?`;
      const whatsappUrl = `https://wa.me/${property.profiles.phone.replace(/\D/g, '')}?text=${encodeURIComponent(message)}`;
      window.open(whatsappUrl, '_blank');
    } else {
      toast({
        title: "Contact information unavailable",
        description: "Property owner's contact information is not available.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading property details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-4">Property Not Found</h2>
              <p className="text-muted-foreground mb-4">
                The property you're looking for doesn't exist or has been removed.
              </p>
              <Button onClick={() => navigate('/')}>Browse Properties</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const primaryImage = property.property_images?.find(img => img.is_primary) || property.property_images?.[0];
  const allImages = property.property_images || [];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-4 sm:py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-4 sm:mb-6">
          <Button variant="outline" size="sm" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Back</span>
            <span className="sm:hidden">Back</span>
          </Button>
          
          {user && (
            <Button
              variant={isFavorite(property.id) ? "default" : "outline"}
              size="sm"
              onClick={handleToggleFavorite}
              className="flex items-center gap-2 w-full sm:w-auto"
            >
              <Heart className={`h-4 w-4 ${isFavorite(property.id) ? 'fill-current' : ''}`} />
              <span className="hidden sm:inline">{isFavorite(property.id) ? 'Favorited' : 'Add to Favorites'}</span>
              <span className="sm:hidden">{isFavorite(property.id) ? 'Saved' : 'Save'}</span>
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-4 sm:space-y-6">
            {/* Image Gallery */}
            <Card>
              <CardContent className="p-0">
                {allImages.length > 0 ? (
                  <div>
                    <AspectRatio ratio={16 / 9}>
                      <img
                        src={allImages[selectedImageIndex]?.image_url || "/placeholder.svg"}
                        alt={property.title}
                        className="w-full h-full object-cover rounded-t-lg"
                      />
                    </AspectRatio>
                    
                    {allImages.length > 1 && (
                      <div className="p-3 sm:p-4">
                        <div className="flex gap-2 overflow-x-auto pb-2">
                          {allImages.map((image, index) => (
                            <button
                              key={image.id}
                              onClick={() => setSelectedImageIndex(index)}
                              className={`flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                                selectedImageIndex === index 
                                  ? 'border-primary' 
                                  : 'border-transparent hover:border-gray-300'
                              }`}
                            >
                              <img
                                src={image.image_url}
                                alt={`Property image ${index + 1}`}
                                className="w-full h-full object-cover"
                              />
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <AspectRatio ratio={16 / 9}>
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center rounded-t-lg">
                      <X className="h-12 w-12 text-gray-400" />
                    </div>
                  </AspectRatio>
                )}
              </CardContent>
            </Card>

            {/* Property Details */}
            <Card>
              <CardHeader className="p-4 sm:p-6">
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                  <div className="flex-1">
                    <CardTitle className="text-xl sm:text-2xl mb-2">{property.title}</CardTitle>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm sm:text-base">{property.location}, {property.county}</span>
                    </div>
                  </div>
                  <div className="text-left lg:text-right">
                    <div className="text-2xl sm:text-3xl font-bold text-primary">
                      KSh {property.rent.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">per month</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
                {/* Property Stats */}
                <div className="grid grid-cols-3 gap-2 sm:gap-4">
                  <div className="text-center p-3 sm:p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Bed className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                    <div className="font-semibold text-sm sm:text-base">{property.bedrooms}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Bedrooms</div>
                  </div>
                  <div className="text-center p-3 sm:p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Bath className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                    <div className="font-semibold text-sm sm:text-base">{property.bathrooms}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">Bathrooms</div>
                  </div>
                  <div className="text-center p-3 sm:p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Square className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2 text-primary" />
                    <div className="font-semibold text-sm sm:text-base">{property.area}</div>
                    <div className="text-xs sm:text-sm text-muted-foreground">sqm</div>
                  </div>
                </div>

                {/* Status Badges */}
                <div className="flex flex-wrap gap-2">
                  {property.available && (
                    <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Available
                    </Badge>
                  )}
                  {property.featured && (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                      <Star className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                </div>

                {/* Description */}
                {property.description && (
                  <div>
                    <h3 className="font-semibold mb-2 text-sm sm:text-base">Description</h3>
                    <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">{property.description}</p>
                  </div>
                )}

                {/* Amenities */}
                {property.amenities && property.amenities.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-3 text-sm sm:text-base">Amenities</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                      {property.amenities.map((amenity, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                          <span className="text-xs sm:text-sm">{amenity}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Property Info */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4 border-t">
                  <div>
                    <h4 className="font-medium mb-1 text-sm sm:text-base">Posted</h4>
                    <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4 flex-shrink-0" />
                      {new Date(property.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-1 text-sm sm:text-base">Last Updated</h4>
                    <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4 flex-shrink-0" />
                      {new Date(property.updated_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Map Section */}
            {(property.latitude && property.longitude) && (
              <Card>
                <CardHeader className="p-4 sm:p-6">
                  <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                    <MapPin className="h-5 w-5" />
                    Property Location
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 sm:p-6">
                  <MapComponent
                    latitude={property.latitude}
                    longitude={property.longitude}
                    location={property.location}
                    title={property.title}
                    className="h-60 sm:h-80 w-full rounded-lg"
                  />
                  <div className="mt-4 flex flex-col sm:flex-row gap-2">
                    <Button onClick={handleGetDirections} className="flex-1">
                      <DirectionIcon className="h-4 w-4 mr-2" />
                      Get Directions
                    </Button>
                    <Button variant="outline" onClick={handleViewOnMap} className="flex-1">
                      <Map className="h-4 w-4 mr-2" />
                      Open in Maps
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-4 sm:space-y-6">
            {/* Contact Owner */}
            {property.profiles && (
              <Card>
                <CardHeader className="p-4 sm:p-6">
                  <CardTitle className="text-lg sm:text-xl">Contact Owner</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 p-4 sm:p-6">
                  <div>
                    <h4 className="font-medium mb-1 text-sm sm:text-base">
                      {property.profiles.full_name || 'Property Owner'}
                    </h4>
                    {property.profiles.phone && (
                      <p className="text-xs sm:text-sm text-muted-foreground">
                        Phone: {' '}
                        <a 
                          href={`tel:${property.profiles.phone.replace(/\D/g, '')}`}
                          className="text-primary hover:underline"
                          aria-label={`Call ${property.profiles.phone}`}
                        >
                          {property.profiles.phone}
                        </a>
                      </p>
                    )}
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-3">
                    {property.profiles.phone && (
                      <Button 
                        className="w-full" 
                        onClick={handleContactOwner}
                        title="Click to call this number"
                        size="sm"
                      >
                        <Phone className="h-4 w-4 mr-2" />
                        <span className="truncate text-sm">{property.profiles.phone}</span>
                      </Button>
                    )}
                    
                    <Button 
                      variant="outline" 
                      className="w-full"
                      onClick={handleEmailOwner}
                      title="Click to compose an email inquiry"
                      size="sm"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      <span className="text-sm">Send Email</span>
                    </Button>
                  </div>
                  
                  <Alert>
                    <AlertDescription className="text-xs sm:text-sm">
                      Always be cautious when sharing personal information and meet in safe, public places.
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            )}

            {/* Location & Directions */}
            <Card>
              <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl">Location & Directions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 p-4 sm:p-6">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-xs sm:text-sm">
                    <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <span>{property.location}, {property.county}</span>
                  </div>
                  
                  {property.latitude && property.longitude && (
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>Coordinates: {property.latitude.toFixed(4)}, {property.longitude.toFixed(4)}</span>
                    </div>
                  )}
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <Button 
                    className="w-full" 
                    onClick={handleGetDirections}
                    disabled={!property.latitude && !property.longitude && !property.location}
                    size="sm"
                  >
                    <DirectionIcon className="h-4 w-4 mr-2" />
                    <span className="text-sm">Get Directions</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="w-full" 
                    onClick={handleViewOnMap}
                    disabled={!property.latitude && !property.longitude && !property.location}
                    size="sm"
                  >
                    <Map className="h-4 w-4 mr-2" />
                    <span className="text-sm">View on Map</span>
                  </Button>
                </div>
                
                <Alert>
                  <AlertDescription className="text-xs">
                    Directions will open in Google Maps. Ensure you have an internet connection.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader className="p-4 sm:p-6">
                <CardTitle className="text-lg sm:text-xl">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 p-4 sm:p-6">
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={handleScheduleViewing}
                  size="sm"
                >
                  <CalendarCheck className="h-4 w-4 mr-2" />
                  <span className="text-sm">Schedule Viewing</span>
                </Button>
                
                {property && (
                  <ShareProperty 
                    property={property} 
                    trigger={
                      <Button variant="outline" className="w-full" size="sm">
                        <Share2 className="h-4 w-4 mr-2" />
                        <span className="text-sm">Share Property</span>
                      </Button>
                    }
                  />
                )}
                
                {property && (
                  <ReportIssue property={property} />
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Similar Properties */}
        {similarProperties.length > 0 && (
          <div className="mt-8 sm:mt-12">
            <h2 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">Similar Properties</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {similarProperties.map((similarProperty) => (
                <PropertyCard
                  key={similarProperty.id}
                  property={similarProperty}
                  onViewDetails={handleSimilarPropertyClick}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PropertyDetail;
