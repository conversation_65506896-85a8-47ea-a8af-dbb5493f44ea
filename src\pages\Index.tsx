import { useState, useEffect, useCallback, useMemo, lazy, Suspense } from "react";
import Navigation from "@/components/Navigation";
import Hero from "@/components/Hero";
import PropertyGrid from "@/components/PropertyGrid";
import { useProperties } from "@/hooks/useProperties";
import { useSearch } from "@/hooks/useSearch";
import { useAuth } from "@/hooks/useAuth";
import { useProfile } from "@/hooks/useProfile";
import { useNavigate } from "react-router-dom";
import { PropertyWithImages, PropertyWithOwner, SearchFilters } from "@/types/property";
import { LoadingSkeleton } from "@/components/ui/loading-skeleton";
import { performanceMonitor } from "@/utils/performance";

// Lazy load heavy components
const PropertyDetail = lazy(() => import("@/components/PropertyDetail"));
const AdvancedSearchFilters = lazy(() => import("@/components/AdvancedSearchFilters").then(module => ({ default: module.AdvancedSearchFilters })));
const PropertyComparison = lazy(() => import("@/components/PropertyComparison").then(module => ({ default: module.PropertyComparison })));
const SavedSearches = lazy(() => import("@/components/SavedSearches").then(module => ({ default: module.SavedSearches })));
const AdminRedirect = lazy(() => import("@/components/AdminRedirect").then(module => ({ default: module.AdminRedirect })));

const Index = () => {
  const { user } = useAuth();
  const { profile } = useProfile();
  const navigate = useNavigate();
  const [selectedProperty, setSelectedProperty] = useState<PropertyWithOwner | null>(null);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [compareProperties, setCompareProperties] = useState<PropertyWithImages[]>([]);
  const [showSavedSearches, setShowSavedSearches] = useState(false);
  const [showAdminRedirect, setShowAdminRedirect] = useState(false);
  const { properties: allProperties, loading: allPropertiesLoading, fetchProperties } = useProperties();
  const { results: searchResults, loading: searchLoading, searchProperties } = useSearch();

  const hasActiveFilters = Object.values(searchFilters).some(value =>
    value !== undefined && value !== '' && (Array.isArray(value) ? value.length > 0 : true)
  );

  // Use search results if filters are applied, otherwise use all properties
  const properties = hasActiveFilters ? searchResults : allProperties;
  const loading = hasActiveFilters ? searchLoading : allPropertiesLoading;

  // Trigger search when filters change
  useEffect(() => {
    if (hasActiveFilters) {
      searchProperties(searchFilters);
    }
  }, [searchFilters, hasActiveFilters, searchProperties]);

  // Check if user is admin and show redirect modal or auto-redirect
  useEffect(() => {
    if (user && profile) {
      if (profile.user_role === 'super_admin') {
        // For super_admin, automatically redirect
        console.log('Super admin detected, redirecting to admin dashboard');
        navigate('/admin');
      } else if (profile.user_role === 'admin') {
        // For regular admin, show the redirect modal
        setShowAdminRedirect(true);
      }
    }
  }, [user, profile, navigate]);

  // Memoized property conversion to avoid recreating objects
  const convertToPropertyWithOwner = useMemo(() => (property: PropertyWithImages): PropertyWithOwner => ({
    ...property,
    profiles: property.profiles || {
      id: 'default-owner',
      full_name: 'Property Owner',
      phone: '+254700000000',
      user_role: 'landlord',
      verification_status: 'pending',
      verification_submitted_at: null,
      verification_completed_at: null,
      verified_by: null,
      national_id: null,
      business_registration_number: null,
      tax_pin: null,
      phone_verified: false,
      email_verified: false,
      phone_verification_code: null,
      phone_verification_expires_at: null,
      business_name: null,
      business_address: null,
      years_in_business: null,
      trust_score: 0.0,
      total_properties: 0,
      successful_rentals: 0,
      admin_notes: null,
      is_flagged: false,
      flagged_reason: null,
      flagged_at: null,
      flagged_by: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  }), []);

  const handleViewDetails = useCallback((propertyId: string) => {
    performanceMonitor.startTimer('property-detail-navigation');
    const property = properties.find(p => p.id === propertyId);
    if (property) {
      const propertyWithOwner = convertToPropertyWithOwner(property);
      setSelectedProperty(propertyWithOwner);
    }
    performanceMonitor.endTimer('property-detail-navigation');
  }, [properties, convertToPropertyWithOwner]);

  // Optimized handlers with useCallback
  const handleBackToGrid = useCallback(() => {
    setSelectedProperty(null);
  }, []);

  const handleAddToCompare = useCallback((property: PropertyWithImages) => {
    setCompareProperties(prev => {
      if (prev.length >= 3) {
        return prev; // Don't show alert, just ignore
      }
      if (prev.some(p => p.id === property.id)) {
        return prev;
      }
      return [...prev, property];
    });
  }, []);

  const handleRemoveFromCompare = useCallback((propertyId: string) => {
    setCompareProperties(prev => prev.filter(p => p.id !== propertyId));
  }, []);

  const handleClearFilters = useCallback(() => {
    setSearchFilters({});
  }, []);

  const handleLoadSavedSearch = useCallback((filters: SearchFilters) => {
    setSearchFilters(filters);
    setShowSavedSearches(false);
  }, []);

  const handleHeroSearch = useCallback((filters: SearchFilters) => {
    setSearchFilters(filters);
  }, []);

  const handleManualSearch = useCallback((filters: SearchFilters) => {
    // Force a search even if filters haven't changed
    searchProperties(filters);
  }, [searchProperties]);

  if (selectedProperty) {
    return (
      <div>
        <Navigation />
        <Suspense fallback={<LoadingSkeleton lines={10} showAvatar />}>
          <PropertyDetail
            property={selectedProperty}
            onBack={handleBackToGrid}
          />
        </Suspense>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <Hero onSearch={handleHeroSearch} />
      
      <div className="container mx-auto px-4 py-8">
        {/* Advanced Search Filters */}
        <Suspense fallback={<LoadingSkeleton lines={3} />}>
          <AdvancedSearchFilters
            filters={searchFilters}
            onFiltersChange={setSearchFilters}
            onClearFilters={handleClearFilters}
            onSearch={handleManualSearch}
            loading={searchLoading}
          />
        </Suspense>

        {/* Saved Searches Toggle */}
        <div className="mb-6 text-center">
          <button
            onClick={() => setShowSavedSearches(!showSavedSearches)}
            className="text-primary hover:underline text-sm"
          >
            {showSavedSearches ? 'Hide' : 'Show'} Saved Searches
          </button>
        </div>

        {/* Saved Searches */}
        {showSavedSearches && (
          <div className="mb-6">
            <Suspense fallback={<LoadingSkeleton lines={2} />}>
              <SavedSearches
                currentFilters={searchFilters}
                onLoadSearch={handleLoadSavedSearch}
              />
            </Suspense>
          </div>
        )}

        {/* Property Comparison */}
        <Suspense fallback={<LoadingSkeleton lines={2} />}>
          <PropertyComparison
            properties={compareProperties}
            onRemoveProperty={handleRemoveFromCompare}
            onClearAll={() => setCompareProperties([])}
          />
        </Suspense>

        {/* Property Grid */}
        <PropertyGrid
          properties={properties}
          onViewDetails={handleViewDetails}
          onAddToCompare={handleAddToCompare}
          compareProperties={compareProperties}
          loading={loading}
        />
      </div>

      {/* Admin Redirect Modal */}
      {showAdminRedirect && profile && (
        <Suspense fallback={<div>Loading...</div>}>
          <AdminRedirect
            userRole={profile.user_role}
            userName={profile.full_name || undefined}
          />
        </Suspense>
      )}
    </div>
  );
};

export default Index;
