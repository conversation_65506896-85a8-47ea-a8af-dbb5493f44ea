import React, { useState, memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertTriangle, Upload, FileText, X, Flag } from 'lucide-react';
import { useVerification } from '@/hooks/useVerification';
import { FraudReportForm as FormData, FraudReportType } from '@/types/verification';

interface FraudReportFormProps {
  reportedUserId: string;
  reportedPropertyId?: string;
  landlordName?: string;
  propertyTitle?: string;
  trigger?: React.ReactNode;
}

const FRAUD_TYPES: { value: FraudReportType; label: string; description: string }[] = [
  {
    value: 'fake_listing',
    label: 'Fake Property Listing',
    description: 'Property does not exist or photos are misleading'
  },
  {
    value: 'fake_landlord',
    label: 'Fake Landlord',
    description: 'Person is impersonating a property owner'
  },
  {
    value: 'scam_attempt',
    label: 'Scam Attempt',
    description: 'Attempted to collect money fraudulently'
  },
  {
    value: 'false_information',
    label: 'False Information',
    description: 'Provided incorrect details about property or terms'
  },
  {
    value: 'other',
    label: 'Other',
    description: 'Other fraudulent or suspicious activity'
  }
];

export const FraudReportForm: React.FC<FraudReportFormProps> = memo(({
  reportedUserId,
  reportedPropertyId,
  landlordName,
  propertyTitle,
  trigger
}) => {
  const { submitFraudReport, loading } = useVerification();
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState<Omit<FormData, 'reported_user_id' | 'reported_property_id'>>({
    reporter_email: '',
    reporter_phone: '',
    report_type: 'fake_listing',
    description: '',
    evidence_files: [],
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleFileSelect = (files: FileList) => {
    const newFiles = Array.from(files);
    const validFiles = newFiles.filter(file => {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        return false;
      }
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        return false;
      }
      return true;
    });

    setFormData(prev => ({
      ...prev,
      evidence_files: [...(prev.evidence_files || []), ...validFiles]
    }));
  };

  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      evidence_files: prev.evidence_files?.filter((_, i) => i !== index) || []
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.reporter_email.trim()) {
      newErrors.reporter_email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.reporter_email)) {
      newErrors.reporter_email = 'Please enter a valid email address';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Please describe the fraudulent activity';
    } else if (formData.description.trim().length < 20) {
      newErrors.description = 'Please provide more details (at least 20 characters)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const success = await submitFraudReport({
      reported_user_id: reportedUserId,
      reported_property_id: reportedPropertyId,
      ...formData,
    });

    if (success) {
      setOpen(false);
      // Reset form
      setFormData({
        reporter_email: '',
        reporter_phone: '',
        report_type: 'fake_listing',
        description: '',
        evidence_files: [],
      });
      setErrors({});
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="text-red-600 border-red-200 hover:bg-red-50">
      <Flag className="h-4 w-4 mr-2" />
      Report Fraud
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Report Fraudulent Activity
          </DialogTitle>
          <DialogDescription>
            Help us keep the platform safe by reporting suspicious or fraudulent activity.
            All reports are investigated thoroughly and kept confidential.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Report Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Report Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {landlordName && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm">
                    <span className="font-medium">Reporting:</span> {landlordName}
                    {propertyTitle && (
                      <>
                        <br />
                        <span className="font-medium">Property:</span> {propertyTitle}
                      </>
                    )}
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="report_type">Type of Fraud</Label>
                <Select 
                  value={formData.report_type} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, report_type: value as FraudReportType }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fraud type" />
                  </SelectTrigger>
                  <SelectContent>
                    {FRAUD_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div>
                          <div className="font-medium">{type.label}</div>
                          <div className="text-sm text-muted-foreground">{type.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Detailed Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Please provide detailed information about the fraudulent activity, including dates, amounts, and any communication you had with the person..."
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className={errors.description ? 'border-red-500' : ''}
                  rows={5}
                />
                {errors.description && (
                  <p className="text-sm text-red-600">{errors.description}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  Minimum 20 characters. Be as specific as possible.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Your Contact Information</CardTitle>
              <CardDescription>
                We may need to contact you for additional information about this report.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="reporter_email">Email Address *</Label>
                  <Input
                    id="reporter_email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.reporter_email}
                    onChange={(e) => setFormData(prev => ({ ...prev, reporter_email: e.target.value }))}
                    className={errors.reporter_email ? 'border-red-500' : ''}
                  />
                  {errors.reporter_email && (
                    <p className="text-sm text-red-600">{errors.reporter_email}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reporter_phone">Phone Number (Optional)</Label>
                  <Input
                    id="reporter_phone"
                    type="tel"
                    placeholder="+254712345678"
                    value={formData.reporter_phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, reporter_phone: e.target.value }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Evidence Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Evidence (Optional)</CardTitle>
              <CardDescription>
                Upload screenshots, documents, or other evidence to support your report.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                <p className="text-sm mb-2">
                  <span className="font-medium">Click to upload</span> or drag and drop
                </p>
                <p className="text-xs text-muted-foreground mb-4">
                  PNG, JPG, or PDF (max 5MB each)
                </p>
                <Input
                  type="file"
                  accept="image/*,.pdf"
                  multiple
                  onChange={(e) => {
                    if (e.target.files) {
                      handleFileSelect(e.target.files);
                    }
                  }}
                  className="hidden"
                  id="evidence-upload"
                />
                <Label htmlFor="evidence-upload" className="cursor-pointer">
                  <Button variant="outline" size="sm" asChild>
                    <span>Choose Files</span>
                  </Button>
                </Label>
              </div>

              {formData.evidence_files && formData.evidence_files.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Uploaded Files:</p>
                  {formData.evidence_files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{file.name}</span>
                        <span className="text-xs text-muted-foreground">
                          ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> Filing false reports is prohibited and may result in account suspension.
              Only report genuine cases of fraud or suspicious activity. All reports are investigated thoroughly.
            </AlertDescription>
          </Alert>

          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading} className="bg-red-600 hover:bg-red-700">
              {loading ? 'Submitting...' : 'Submit Report'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
});

FraudReportForm.displayName = "FraudReportForm";
