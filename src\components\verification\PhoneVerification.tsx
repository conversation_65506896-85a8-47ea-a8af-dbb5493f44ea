import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Phone, CheckCircle, Clock, AlertTriangle, RefreshCw } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface PhoneVerificationProps {
  phoneNumber: string;
  onVerificationComplete?: (verified: boolean) => void;
  showTitle?: boolean;
}

export const PhoneVerification: React.FC<PhoneVerificationProps> = ({
  phoneNumber,
  onVerificationComplete,
  showTitle = true
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [verificationCode, setVerificationCode] = useState('');
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [canResend, setCanResend] = useState(true);

  useEffect(() => {
    // Check if phone is already verified
    checkVerificationStatus();
  }, [user]);

  useEffect(() => {
    // Countdown timer for resend button
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && isCodeSent) {
      setCanResend(true);
    }
  }, [timeLeft, isCodeSent]);

  const checkVerificationStatus = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('phone_verified')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      
      if (data.phone_verified) {
        setIsVerified(true);
        onVerificationComplete?.(true);
      }
    } catch (error) {
      console.error('Error checking verification status:', error);
    }
  };

  const generateVerificationCode = () => {
    return Math.floor(100000 + Math.random() * 900000).toString();
  };

  const sendVerificationCode = async () => {
    if (!user || !phoneNumber) return;

    setIsSendingCode(true);
    try {
      // Generate a 6-digit code
      const code = generateVerificationCode();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now

      // Store the code in the database
      const { error } = await supabase
        .from('profiles')
        .update({
          phone_verification_code: code,
          phone_verification_expires_at: expiresAt.toISOString(),
        })
        .eq('id', user.id);

      if (error) throw error;

      // In a real implementation, you would send the SMS here
      // For demo purposes, we'll show the code in the toast
      
      toast({
        title: "Verification code sent",
        description: `A 6-digit code has been sent to ${phoneNumber}.`,
      });

      setIsCodeSent(true);
      setTimeLeft(60); // 60 seconds cooldown
      setCanResend(false);
    } catch (error) {
      console.error('Error sending verification code:', error);
      toast({
        title: "Failed to send code",
        description: error instanceof Error ? error.message : "Please try again",
        variant: "destructive",
      });
    } finally {
      setIsSendingCode(false);
    }
  };

  const verifyCode = async () => {
    if (!user || !verificationCode) return;

    setIsVerifying(true);
    try {
      // Get the stored code and check if it's valid
      const { data, error } = await supabase
        .from('profiles')
        .select('phone_verification_code, phone_verification_expires_at')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      const now = new Date();
      const expiresAt = new Date(data.phone_verification_expires_at);

      if (now > expiresAt) {
        toast({
          title: "Code expired",
          description: "The verification code has expired. Please request a new one.",
          variant: "destructive",
        });
        return;
      }

      if (data.phone_verification_code !== verificationCode) {
        toast({
          title: "Invalid code",
          description: "The verification code is incorrect. Please try again.",
          variant: "destructive",
        });
        return;
      }

      // Mark phone as verified
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          phone_verified: true,
          phone_verification_code: null,
          phone_verification_expires_at: null,
        })
        .eq('id', user.id);

      if (updateError) throw updateError;

      setIsVerified(true);
      toast({
        title: "Phone verified",
        description: "Your phone number has been successfully verified!",
      });

      onVerificationComplete?.(true);
    } catch (error) {
      console.error('Error verifying code:', error);
      toast({
        title: "Verification failed",
        description: error instanceof Error ? error.message : "Please try again",
        variant: "destructive",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  if (isVerified) {
    return (
      <Card>
        {showTitle && (
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Phone Verified
            </CardTitle>
            <CardDescription>
              Your phone number has been successfully verified
            </CardDescription>
          </CardHeader>
        )}
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">{phoneNumber}</span>
            </div>
            <Badge variant="outline" className="text-green-600 border-green-600">
              <CheckCircle className="h-3 w-3 mr-1" />
              Verified
            </Badge>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      {showTitle && (
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Phone Verification
          </CardTitle>
          <CardDescription>
            Verify your phone number to enhance account security
          </CardDescription>
        </CardHeader>
      )}
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between p-3 bg-gray-50 border rounded-lg">
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{phoneNumber}</span>
          </div>
          <Badge variant="outline" className="text-yellow-600 border-yellow-600">
            <Clock className="h-3 w-3 mr-1" />
            Unverified
          </Badge>
        </div>

        {!isCodeSent ? (
          <div className="space-y-4">
            <Alert>
              <Phone className="h-4 w-4" />
              <AlertDescription>
                We'll send a 6-digit verification code to your phone number via SMS.
              </AlertDescription>
            </Alert>
            
            <Button 
              onClick={sendVerificationCode} 
              disabled={isSendingCode}
              className="w-full"
            >
              {isSendingCode ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Sending Code...
                </>
              ) : (
                <>
                  <Phone className="h-4 w-4 mr-2" />
                  Send Verification Code
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                A 6-digit verification code has been sent to {phoneNumber}. 
                Enter the code below to verify your phone number.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <Label htmlFor="verification-code">Verification Code</Label>
              <Input
                id="verification-code"
                type="text"
                placeholder="Enter 6-digit code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                maxLength={6}
                className="text-center text-lg tracking-widest"
              />
            </div>

            <div className="flex gap-3">
              <Button 
                onClick={verifyCode} 
                disabled={isVerifying || verificationCode.length !== 6}
                className="flex-1"
              >
                {isVerifying ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Verify Code
                  </>
                )}
              </Button>

              <Button 
                variant="outline" 
                onClick={sendVerificationCode}
                disabled={!canResend || isSendingCode}
              >
                {!canResend ? (
                  `Resend (${timeLeft}s)`
                ) : isSendingCode ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  'Resend'
                )}
              </Button>
            </div>

            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Didn't receive the code? Check your spam folder or try resending.
              </p>
            </div>
          </div>
        )}

        {/* Demo Notice */}
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Demo Mode:</strong> In a production environment, SMS would be sent via a service like Twilio. 
            For this demo, the verification code is logged to the browser console.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};
