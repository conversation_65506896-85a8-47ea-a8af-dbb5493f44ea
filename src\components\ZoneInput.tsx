import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, X } from 'lucide-react';
import { getZonesForCounty, searchZones } from '@/utils/zones';

interface ZoneInputProps {
  value: string;
  onChange: (value: string) => void;
  county?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const ZoneInput: React.FC<ZoneInputProps> = ({
  value,
  onChange,
  county,
  placeholder = "Enter zone or area",
  className,
  disabled = false
}) => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Update suggestions when county or input value changes
  useEffect(() => {
    if (inputValue.trim().length > 0) {
      const filteredSuggestions = county 
        ? searchZones(inputValue, county)
        : searchZones(inputValue);
      setSuggestions(filteredSuggestions.slice(0, 8)); // Limit to 8 suggestions
    } else if (county) {
      // Show popular zones for the county when input is empty
      const countyZones = getZonesForCounty(county);
      setSuggestions(countyZones.slice(0, 8));
    } else {
      setSuggestions([]);
    }
  }, [inputValue, county]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange(newValue);
    setShowSuggestions(true);
  };

  // Handle suggestion selection
  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    onChange(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  // Handle input focus
  const handleFocus = () => {
    setShowSuggestions(true);
  };

  // Handle input blur (with delay to allow suggestion clicks)
  const handleBlur = () => {
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  };

  // Clear input
  const handleClear = () => {
    setInputValue('');
    onChange('');
    inputRef.current?.focus();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  return (
    <div className="relative">
      <div className="relative">
        <Input
          ref={inputRef}
          value={inputValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={className}
          disabled={disabled}
        />
        {inputValue && !disabled && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100"
            onClick={handleClear}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <Card className="absolute top-full left-0 right-0 z-50 mt-1 max-h-60 overflow-y-auto">
          <CardContent className="p-2">
            <div className="space-y-1">
              {county && (
                <div className="px-2 py-1 text-xs text-gray-500 font-medium">
                  Popular areas in {county}:
                </div>
              )}
              {suggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  className="w-full justify-start h-auto p-2 text-left hover:bg-gray-50"
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <MapPin className="h-3 w-3 mr-2 text-gray-400" />
                  <span className="text-sm">{suggestion}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Popular zones for county (when no input) */}
      {county && !inputValue && !showSuggestions && (
        <div className="mt-2">
          <div className="text-xs text-gray-500 mb-2">Popular areas in {county}:</div>
          <div className="flex flex-wrap gap-1">
            {getZonesForCounty(county).slice(0, 6).map((zone, index) => (
              <Badge
                key={index}
                variant="outline"
                className="cursor-pointer hover:bg-gray-50 text-xs"
                onClick={() => handleSuggestionClick(zone)}
              >
                {zone}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ZoneInput;
