import React, { useState, useEffect, memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  ShieldCheck, 
  ShieldAlert, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  MapPin, 
  Camera, 
  FileText,
  Flag,
  Eye
} from 'lucide-react';
import { useVerification } from '@/hooks/useVerification';
import { PropertySafetyCheck as SafetyData } from '@/types/verification';
import { FraudReportForm } from './FraudReportForm';

interface PropertySafetyCheckProps {
  propertyId: string;
  landlordId: string;
  landlordName?: string;
  propertyTitle?: string;
  showDetailed?: boolean;
}

const getSafetyLevel = (score: number) => {
  if (score >= 80) return { level: 'high', color: 'text-green-600', bgColor: 'bg-green-100', icon: ShieldCheck };
  if (score >= 60) return { level: 'medium', color: 'text-yellow-600', bgColor: 'bg-yellow-100', icon: Shield };
  return { level: 'low', color: 'text-red-600', bgColor: 'bg-red-100', icon: ShieldAlert };
};

const getSafetyBadge = (score: number) => {
  const safety = getSafetyLevel(score);
  const Icon = safety.icon;
  
  return (
    <Badge variant="outline" className={`${safety.color} border-current`}>
      <Icon className="h-3 w-3 mr-1" />
      {safety.level === 'high' ? 'Safe' : safety.level === 'medium' ? 'Caution' : 'High Risk'}
    </Badge>
  );
};

export const PropertySafetyCheck: React.FC<PropertySafetyCheckProps> = memo(({
  propertyId,
  landlordId,
  landlordName,
  propertyTitle,
  showDetailed = false
}) => {
  const { getPropertySafetyCheck } = useVerification();
  const [safetyData, setSafetyData] = useState<SafetyData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDetails, setShowDetails] = useState(showDetailed);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);

  useEffect(() => {
    let isMounted = true;
    
    const fetchSafetyData = async () => {
      if (!hasLoadedOnce) {
        setLoading(true);
        try {
          const data = await getPropertySafetyCheck(propertyId);
          if (isMounted) {
            setSafetyData(data);
            setHasLoadedOnce(true);
          }
        } catch (error) {
          console.error('Error loading safety data:', error);
          if (isMounted) {
            setSafetyData(null);
            setHasLoadedOnce(true);
          }
        } finally {
          if (isMounted) {
            setLoading(false);
          }
        }
      }
    };

    fetchSafetyData();

    return () => {
      isMounted = false;
    };
  }, [propertyId, getPropertySafetyCheck, hasLoadedOnce]);

  if (loading && !hasLoadedOnce) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            <div className="h-2 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!safetyData) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>Unable to load safety information</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const safety = getSafetyLevel(safetyData.safety_score);
  const Icon = safety.icon;

  if (!showDetails) {
    return (
      <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setShowDetails(true)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full ${safety.bgColor}`}>
                <Icon className={`h-5 w-5 ${safety.color}`} />
              </div>
              <div>
                <p className="font-medium">Safety Score</p>
                <p className="text-sm text-muted-foreground">
                  {safetyData.safety_score}/100
                </p>
              </div>
            </div>
            <div className="text-right">
              {getSafetyBadge(safetyData.safety_score)}
              <p className="text-xs text-muted-foreground mt-1">
                <Eye className="h-3 w-3 inline mr-1" />
                Click for details
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon className={`h-5 w-5 ${safety.color}`} />
            Property Safety Check
          </div>
          {getSafetyBadge(safetyData.safety_score)}
        </CardTitle>
        <CardDescription>
          Comprehensive safety assessment based on verification status and fraud reports
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Safety Score */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Safety Score</span>
            <span className="text-lg font-bold">{safetyData.safety_score}/100</span>
          </div>
          <Progress value={safetyData.safety_score} className="h-2" />
          <p className="text-sm text-muted-foreground">
            Based on owner verification, property documentation, and fraud reports
          </p>
        </div>

        {/* Verification Checks */}
        <div className="space-y-3">
          <h4 className="font-medium">Verification Status</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-center gap-2">
              {safetyData.owner_verified ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <span className="text-sm">Owner Identity Verified</span>
            </div>

            <div className="flex items-center gap-2">
              {safetyData.ownership_documents_verified ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <span className="text-sm">Ownership Documents</span>
            </div>

            <div className="flex items-center gap-2">
              {safetyData.location_verified ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <span className="text-sm">Location Verified</span>
            </div>

            <div className="flex items-center gap-2">
              {safetyData.photos_authentic ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <span className="text-sm">Photos Authentic</span>
            </div>
          </div>
        </div>

        {/* Fraud Reports */}
        {safetyData.fraud_reports_count > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Warning:</strong> This property has {safetyData.fraud_reports_count} active fraud report(s).
              Exercise extreme caution and verify all information independently.
            </AlertDescription>
          </Alert>
        )}

        {/* Warnings */}
        {safetyData.warnings.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-red-600">⚠️ Safety Warnings</h4>
            <div className="space-y-1">
              {safetyData.warnings.map((warning, index) => (
                <div key={index} className="flex items-start gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                  <span>{warning}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recommendations */}
        {safetyData.recommendations.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-blue-600">💡 Safety Recommendations</h4>
            <div className="space-y-1">
              {safetyData.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>{recommendation}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Safety Tips */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">🛡️ General Safety Tips</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Always visit the property in person before making any payments</li>
            <li>• Verify the landlord's identity and ownership documents</li>
            <li>• Never send money without seeing the property first</li>
            <li>• Use secure payment methods and keep all receipts</li>
            <li>• Trust your instincts - if something feels wrong, investigate further</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t">
          <FraudReportForm
            reportedUserId={landlordId}
            reportedPropertyId={propertyId}
            landlordName={landlordName}
            propertyTitle={propertyTitle}
            trigger={
              <Button variant="outline" size="sm" className="text-red-600 border-red-200 hover:bg-red-50">
                <Flag className="h-4 w-4 mr-2" />
                Report Issue
              </Button>
            }
          />
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setShowDetails(false)}
          >
            <Eye className="h-4 w-4 mr-2" />
            Minimize
          </Button>
        </div>
      </CardContent>
    </Card>
  );
});

PropertySafetyCheck.displayName = "PropertySafetyCheck";
