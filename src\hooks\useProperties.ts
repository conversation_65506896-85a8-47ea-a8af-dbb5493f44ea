import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from './use-toast';
import { 
  PropertyWithImages, 
  PropertyWithOwner, 
  PropertyFormData,
  PropertyWithUnits,
  SearchFilters,
  Profile
} from '@/types/property';

export const useProperties = () => {
  const [properties, setProperties] = useState<PropertyWithImages[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  const fetchProperties = useCallback(async (filters?: SearchFilters) => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('properties')
        .select(`
          *,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          )
        `)
        .eq('available', true)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters) {
        if (filters.county) {
          query = query.eq('county', filters.county);
        }
        if (filters.zone) {
          query = query.ilike('zone', `%${filters.zone}%`);
        }
        if (filters.location) {
          query = query.ilike('location', `%${filters.location}%`);
        }
        if (filters.minRent) {
          query = query.gte('rent', filters.minRent);
        }
        if (filters.maxRent) {
          query = query.lte('rent', filters.maxRent);
        }
        if (filters.bedrooms) {
          query = query.eq('bedrooms', filters.bedrooms);
        }
        if (filters.bathrooms) {
          query = query.eq('bathrooms', filters.bathrooms);
        }
        if (filters.minArea) {
          query = query.gte('area', filters.minArea);
        }
        if (filters.maxArea) {
          query = query.lte('area', filters.maxArea);
        }
        if (filters.featured !== undefined) {
          query = query.eq('featured', filters.featured);
        }
        if (filters.amenities && filters.amenities.length > 0) {
          query = query.contains('amenities', filters.amenities);
        }
      }

      const { data: propertiesData, error } = await query;

      if (error) {
        console.error('Supabase error details:', error);
        throw error;
      }

      if (!propertiesData || propertiesData.length === 0) {
        setProperties([]);
        return;
      }

      // Get unique user IDs from the properties
      const userIds = [...new Set(propertiesData.map(p => p.user_id))];
      
      // Fetch profiles for these users
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name, phone, created_at, updated_at')
        .in('id', userIds);

      if (profilesError) {
        console.error('Error fetching profiles:', profilesError);
        // Continue without profiles data
      }

      // Combine properties with profiles
      const propertiesWithProfiles = propertiesData.map(property => ({
        ...property,
        profiles: profilesData?.find(profile => profile.id === property.user_id) || null
      }));

      setProperties((propertiesWithProfiles as unknown as PropertyWithImages[]) || []);
    } catch (error) {
      console.error('Error fetching properties:', error);
      
      toast({
        title: "Error fetching properties",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      
      setProperties([]);
    } finally {
      setLoading(false);
    }
  }, [toast]);



  const fetchUserProperties = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('properties')
        .select(`
          id,
          user_id,
          title,
          description,
          rent,
          location,
          county,
          zone,
          room_type,
          bedrooms,
          bathrooms,
          area,
          amenities,
          available,
          featured,
          latitude,
          longitude,
          formatted_address,
          neighborhood,
          city,
          created_at,
          updated_at,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase error fetching user properties:', error);
        throw error;
      }

      setProperties((data as unknown as PropertyWithImages[]) || []);
    } catch (error) {
      console.error('Error fetching user properties:', error);
      
      // Fallback to empty array for user properties when there's an error
      setProperties([]);
      
      toast({
        title: "Error fetching your properties",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user, toast]);

  const getPropertyById = useCallback(async (id: string): Promise<PropertyWithOwner | null> => {
    try {
      const { data: propertyData, error } = await supabase
        .from('properties')
        .select(`
          id,
          user_id,
          title,
          description,
          rent,
          location,
          county,
          zone,
          room_type,
          bedrooms,
          bathrooms,
          area,
          amenities,
          available,
          featured,
          latitude,
          longitude,
          formatted_address,
          neighborhood,
          city,
          created_at,
          updated_at,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Supabase error fetching property by ID:', error);
        throw error;
      }

      if (!propertyData) {
        return null;
      }

      // Fetch the profile for this property's owner
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id, full_name, phone, created_at, updated_at')
        .eq('id', propertyData.user_id)
        .maybeSingle();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Error fetching profile:', profileError);
        // Continue without profile data
      }

      // Combine property with profile
      const propertyWithOwner = {
        ...propertyData,
        profiles: profileData || null
      };

      return propertyWithOwner as unknown as PropertyWithOwner;
    } catch (error) {
      console.error('Error fetching property:', error);
      
      toast({
        title: "Property not found",
        description: "The requested property could not be found",
        variant: "destructive",
      });
      return null;
    }
  }, [toast]);

  const createProperty = async (propertyData: PropertyFormData) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please log in to create a property",
        variant: "destructive",
      });
      return null;
    }

    try {
      console.log('Creating property with data:', propertyData);
      
      // Create the main property record
      const { data: property, error: propertyError } = await supabase
        .from('properties')
        .insert([{ 
          ...propertyData, 
          user_id: user.id,
          total_units: propertyData.property_type === 'multi_unit' ? propertyData.units?.length || 0 : 1
        }])
        .select()
        .single();

      if (propertyError) {
        console.error('Property creation error:', propertyError);
        throw new Error(`Database error: ${propertyError.message}`);
      }

      console.log('Property created successfully:', property);

      // If it's a multi-unit property, create the units
      if (propertyData.property_type === 'multi_unit' && propertyData.units && propertyData.units.length > 0) {
        const unitsToInsert = propertyData.units.map(unit => ({
          property_id: property.id,
          unit_name: unit.unit_name,
          unit_number: unit.unit_number,
          room_type: unit.room_type,
          bedrooms: unit.bedrooms,
          bathrooms: unit.bathrooms,
          area: unit.area,
          rent: unit.rent,
          deposit: unit.deposit,
          is_available: unit.is_available,
          unit_amenities: unit.unit_amenities,
          unit_description: unit.unit_description,
          floor_number: unit.floor_number,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));

        console.log('Creating units:', unitsToInsert);

        const { error: unitsError } = await supabase
          .from('property_units')
          .insert(unitsToInsert);

        if (unitsError) {
          // If unit creation fails, we should probably delete the property
          console.error('Error creating units:', unitsError);
          await supabase.from('properties').delete().eq('id', property.id);
          throw new Error(`Units creation error: ${unitsError.message}`);
        }

        console.log('Units created successfully');
      } else if (propertyData.property_type === 'single_unit') {
        // For single unit properties, create one unit record
        const singleUnit = {
          property_id: property.id,
          unit_name: 'Main Unit',
          unit_number: '1',
          room_type: propertyData.room_type || '',
          bedrooms: propertyData.bedrooms || 1,
          bathrooms: propertyData.bathrooms || 1,
          area: propertyData.area || 0,
          rent: propertyData.rent || 0,
          deposit: 0,
          is_available: propertyData.available ?? true,
          unit_amenities: [],
          unit_description: propertyData.description || '',
          floor_number: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { error: unitError } = await supabase
          .from('property_units')
          .insert([singleUnit]);

        if (unitError) {
          console.error('Error creating single unit:', unitError);
          await supabase.from('properties').delete().eq('id', property.id);
          throw new Error(`Single unit creation error: ${unitError.message}`);
        }
      }

      toast({
        title: "Property created",
        description: "Your property has been listed successfully",
      });

      return property;
    } catch (error) {
      console.error('Full error details:', error);
      toast({
        title: "Error creating property",
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: "destructive",
      });
      throw error; // Re-throw the error so the calling component can handle it
    }
  };

  const updateProperty = async (id: string, updates: Partial<PropertyFormData>) => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('properties')
        .update(updates)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Property updated",
        description: "Your property has been updated successfully",
      });

      return data;
    } catch (error) {
      toast({
        title: "Error updating property",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return null;
    }
  };

  const deleteProperty = async (id: string) => {
    if (!user) return false;

    try {
      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;

      toast({
        title: "Property deleted",
        description: "Your property has been removed",
      });

      return true;
    } catch (error) {
      toast({
        title: "Error deleting property",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return false;
    }
  };

  const uploadPropertyImage = async (propertyId: string, file: File, isPrimary = false) => {
    if (!user) return null;

    try {
      // Upload file to Supabase Storage with user ID in the path
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${propertyId}/${Date.now()}.${fileExt}`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('property-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('Storage upload error:', uploadError);
        throw uploadError;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('property-images')
        .getPublicUrl(fileName);

      // Save image record to database
      const { data, error } = await supabase
        .from('property_images')
        .insert([{
          property_id: propertyId,
          image_url: publicUrl,
          is_primary: isPrimary
        }])
        .select()
        .single();

      if (error) {
        console.error('Database insert error:', error);
        throw error;
      }

      toast({
        title: "Image uploaded",
        description: "Property image has been uploaded successfully",
      });

      return data;
    } catch (error) {
      console.error('Upload error details:', error);
      toast({
        title: "Error uploading image",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return null;
    }
  };

  const deletePropertyImage = async (imageId: string) => {
    if (!user) return false;

    try {
      const { error } = await supabase
        .from('property_images')
        .delete()
        .eq('id', imageId);

      if (error) throw error;

      toast({
        title: "Image deleted",
        description: "Property image has been removed",
      });

      return true;
    } catch (error) {
      toast({
        title: "Error deleting image",
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: "destructive",
      });
      return false;
    }
  };

  const getPropertyWithUnits = async (propertyId: string): Promise<PropertyWithUnits | null> => {
    try {
      const { data: property, error: propertyError } = await supabase
        .from('properties')
        .select(`
          *,
          property_images:property_images (
            id,
            property_id,
            image_url,
            is_primary,
            created_at
          ),
          property_units:property_units (
            id,
            property_id,
            unit_name,
            unit_number,
            room_type,
            bedrooms,
            bathrooms,
            area,
            rent,
            deposit,
            is_available,
            unit_amenities,
            unit_description,
            floor_number,
            created_at,
            updated_at,
            unit_images:unit_images (
              id,
              unit_id,
              image_url,
              is_primary,
              created_at
            )
          ),
          profiles:profiles (
            id,
            full_name,
            phone,
            created_at,
            updated_at
          )
        `)
        .eq('id', propertyId)
        .single();

      if (propertyError) throw propertyError;

      return property as unknown as PropertyWithUnits;
    } catch (error) {
      console.error('Error fetching property with units:', error);
      toast({
        title: "Error fetching property",
        description: "Could not load property details",
        variant: "destructive",
      });
      return null;
    }
  };

  useEffect(() => {
    fetchProperties();
  }, [fetchProperties]);

  return {
    properties,
    loading,
    fetchProperties,
    fetchUserProperties,
    getPropertyById,
    getPropertyWithUnits,
    createProperty,
    updateProperty,
    deleteProperty,
    uploadPropertyImage,
    deletePropertyImage,
  };
};
