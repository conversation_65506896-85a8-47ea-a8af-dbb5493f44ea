// Common zones/areas for different counties in Kenya
// This helps landlords with zone suggestions when adding properties

export const COUNTY_ZONES: Record<string, string[]> = {
  'Nairobi': [
    'Westlands',
    'Karen',
    'Kilimani',
    'Kileleshwa',
    'Lavington',
    'Runda',
    'Muthaiga',
    'Spring Valley',
    'Riverside',
    'Parklands',
    'Highridge',
    'Loresho',
    'G<PERSON><PERSON>',
    'Kitisuru',
    'Ridgeways',
    'Rosslyn',
    'Hurlingham',
    'Kilimani',
    'Kirichwa Road',
    'Wood Avenue',
    'Dennis Pritt Road',
    'Ngong Road',
    'Lang\'ata Road',
    'Mbagathi Way',
    'South B',
    'South C',
    'Nyayo Estate',
    'Pipeline',
    'Embakasi',
    'Donholm',
    'Buruburu',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>mb<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>a',
    'Ruiru',
    'Kiambu Road',
    'Thika Road',
    'Eastleigh',
    'Pangani',
    '<PERSON><PERSON><PERSON>',
    'Makadara',
    'Industrial Area',
    'CBD',
    'Upper Hill',
    '<PERSON><PERSON><PERSON>',
    'State House Road'
  ],
  'Mombasa': [
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    'Mtwapa',
    'Kilifi',
    'Malindi',
    'Old Town',
    'Ganjoni',
    'Mkomani',
    'Changamwe',
    'Port Reitz',
    'Likoni',
    'Shelly Beach'
  ],
  'Kisumu': [
    'Milimani',
    'Nyalenda',
    'Kondele',
    'Mamboleo',
    'Tom Mboya',
    'Oginga Odinga',
    'Kakamega Road',
    'Kisumu CBD',
    'Dunga',
    'Nyamasaria'
  ],
  'Nakuru': [
    'Milimani',
    'Section 58',
    'Bondeni',
    'Kaptembwo',
    'Bahati',
    'London',
    'Shabab',
    'Nakuru CBD',
    'Pipeline',
    'Free Area'
  ],
  'Eldoret': [
    'Elgon View',
    'Pioneer',
    'Kapsoya',
    'Langas',
    'West Indies',
    'Eldoret CBD',
    'Kimumu',
    'Chepkoilel'
  ],
  'Thika': [
    'Makongeni',
    'Kiganjo',
    'Landless',
    'Thika CBD',
    'Kiandutu',
    'Gatuanyaga'
  ],
  'Machakos': [
    'Machakos CBD',
    'Mumbuni',
    'Kola',
    'Kalama',
    'Mua Hills'
  ],
  'Meru': [
    'Meru CBD',
    'Makutano',
    'Nkubu',
    'Timau',
    'Maua'
  ],
  'Nyeri': [
    'Nyeri CBD',
    'Mathari',
    'Kingongo',
    'Ruring\'u',
    'Karatina'
  ],
  'Kiambu': [
    'Kiambu CBD',
    'Ruiru',
    'Thika',
    'Limuru',
    'Kikuyu',
    'Banana',
    'Githunguri'
  ]
};

// Get zones for a specific county
export const getZonesForCounty = (county: string): string[] => {
  return COUNTY_ZONES[county] || [];
};

// Get all unique zones across all counties
export const getAllZones = (): string[] => {
  const allZones = Object.values(COUNTY_ZONES).flat();
  return [...new Set(allZones)].sort();
};

// Search zones by partial match
export const searchZones = (query: string, county?: string): string[] => {
  const zones = county ? getZonesForCounty(county) : getAllZones();
  return zones.filter(zone => 
    zone.toLowerCase().includes(query.toLowerCase())
  );
};

// Check if a zone exists in a county
export const isValidZoneForCounty = (zone: string, county: string): boolean => {
  const countyZones = getZonesForCounty(county);
  return countyZones.some(z => z.toLowerCase() === zone.toLowerCase());
};

// Get popular zones (most commonly used across counties)
export const getPopularZones = (): string[] => {
  const zoneCount: Record<string, number> = {};
  
  Object.values(COUNTY_ZONES).forEach(zones => {
    zones.forEach(zone => {
      zoneCount[zone] = (zoneCount[zone] || 0) + 1;
    });
  });
  
  return Object.entries(zoneCount)
    .filter(([_, count]) => count > 1)
    .sort(([_, a], [__, b]) => b - a)
    .map(([zone]) => zone)
    .slice(0, 20);
};

export default {
  COUNTY_ZONES,
  getZonesForCounty,
  getAllZones,
  searchZones,
  isValidZoneForCounty,
  getPopularZones
};
