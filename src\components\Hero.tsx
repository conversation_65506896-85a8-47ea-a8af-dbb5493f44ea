import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, MapPin } from "lucide-react";
import heroImage from "@/assets/hero-house.jpg";
import { SearchFilters } from "@/types/property";

interface HeroProps {
  onSearch?: (filters: SearchFilters) => void;
}

const Hero = ({ onSearch }: HeroProps) => {
  const [location, setLocation] = useState("");
  const [maxBudget, setMaxBudget] = useState("");

  const handleSearch = () => {
    const filters: SearchFilters = {};

    if (location.trim()) {
      filters.location = location.trim();
    }

    if (maxBudget && !isNaN(Number(maxBudget))) {
      filters.maxRent = Number(maxBudget);
    }

    if (onSearch) {
      onSearch(filters);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };
  return (
    <section className="relative min-h-[500px] sm:min-h-[600px] flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${heroImage})`,
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/20" />
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 text-center text-white">
        <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 leading-tight">
          Find Your Perfect
          <span className="block text-primary-light">Rental Home</span>
          in Kenya
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl mb-6 sm:mb-8 opacity-90 max-w-2xl mx-auto px-4">
          Discover thousands of quality rental properties across Kenya. Your dream home is just a search away.
        </p>
        
        {/* User Type Information */}
        <div className="mb-6 sm:mb-8 max-w-3xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 text-sm">
            <div className="bg-white/10 backdrop-blur rounded-lg p-3 sm:p-4 border border-white/20">
              <h3 className="font-semibold mb-2">🏠 Looking for a Home?</h3>
              <p className="opacity-90 text-xs sm:text-sm">Browse thousands of properties for free. No account needed!</p>
            </div>
            <div className="bg-white/10 backdrop-blur rounded-lg p-3 sm:p-4 border border-white/20">
              <h3 className="font-semibold mb-2">🏢 Property Owner?</h3>
              <p className="opacity-90 text-xs sm:text-sm">Create an account to list and manage your rental properties.</p>
            </div>
          </div>
        </div>

        {/* Search Box */}
        <div className="max-w-4xl mx-auto bg-white/95 backdrop-blur rounded-xl p-4 sm:p-6 shadow-2xl">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            <div className="relative sm:col-span-2 lg:col-span-1">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4 sm:h-5 sm:w-5" />
              <Input
                placeholder="Location (e.g., Nairobi, Mombasa)"
                className="pl-9 sm:pl-10 h-10 sm:h-12 text-foreground text-sm sm:text-base"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>
            <div className="relative">
              <Input
                placeholder="Max Budget (KSH)"
                className="h-10 sm:h-12 text-foreground text-sm sm:text-base"
                type="number"
                value={maxBudget}
                onChange={(e) => setMaxBudget(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>
            <Button
              size="lg"
              className="h-10 sm:h-12 bg-gradient-to-r from-primary to-primary-light hover:from-primary-dark hover:to-primary text-sm sm:text-base sm:col-span-2 lg:col-span-1"
              onClick={handleSearch}
            >
              <Search className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
              Search Properties
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-8 mt-8 sm:mt-12 max-w-2xl mx-auto px-4">
          <div>
            <div className="text-2xl sm:text-3xl font-bold text-primary-light">1000+</div>
            <div className="text-xs sm:text-sm opacity-80">Properties</div>
          </div>
          <div>
            <div className="text-2xl sm:text-3xl font-bold text-primary-light">47</div>
            <div className="text-xs sm:text-sm opacity-80">Counties</div>
          </div>
          <div>
            <div className="text-2xl sm:text-3xl font-bold text-primary-light">5000+</div>
            <div className="text-xs sm:text-sm opacity-80">Happy Tenants</div>
          </div>
          <div>
            <div className="text-2xl sm:text-3xl font-bold text-primary-light">24/7</div>
            <div className="text-xs sm:text-sm opacity-80">Support</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;