import { useState } from "react";
import { PropertyWithImages } from "@/types/property";
import PropertyCard from "./PropertyCard";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { SlidersHorizontal, Plus } from "lucide-react";

interface PropertyGridProps {
  properties: PropertyWithImages[];
  onViewDetails: (propertyId: string) => void;
  onAddToCompare?: (property: PropertyWithImages) => void;
  compareProperties?: PropertyWithImages[];
  loading?: boolean;
}

const PropertyGrid = ({
  properties,
  onViewDetails,
  onAddToCompare,
  compareProperties = [],
  loading = false
}: PropertyGridProps) => {
  const [sortBy, setSortBy] = useState("recent");

  // Since filtering is now handled at the database level through search,
  // we just need to handle sorting of the received properties
  const sortedProperties = [...properties].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.rent - b.rent;
      case "price-high":
        return b.rent - a.rent;
      case "recent":
      default:
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    }
  });

  const featuredProperties = sortedProperties.filter(p => p.featured);
  const regularProperties = sortedProperties.filter(p => !p.featured);

  return (
    <section className="py-8 sm:py-12 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-6 sm:mb-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-foreground mb-2 sm:mb-4">
            Available Properties
          </h2>
          <p className="text-muted-foreground text-base sm:text-lg max-w-2xl mx-auto px-4">
            Browse our carefully curated selection of rental properties across Kenya
          </p>
        </div>

        {/* Results Summary and Sort */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 sm:mb-8">
          <div className="text-sm text-muted-foreground">
            Showing {properties.length} properties
          </div>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Most Recent</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Featured Properties */}
        {featuredProperties.length > 0 && (
          <div className="mb-8 sm:mb-12">
            <h3 className="text-xl sm:text-2xl font-semibold text-foreground mb-4 sm:mb-6 flex items-center">
              <SlidersHorizontal className="h-5 w-5 sm:h-6 sm:w-6 mr-2 text-primary" />
              Featured Properties
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {featuredProperties.map((property) => (
                <div key={property.id} className="relative">
                  <PropertyCard
                    property={property}
                    onViewDetails={onViewDetails}
                  />
                  {onAddToCompare && (
                    <div className="absolute top-2 right-2">
                      {compareProperties.find(p => p.id === property.id) ? (
                        <Badge className="bg-blue-600">
                          ✓ In Compare
                        </Badge>
                      ) : (
                        <Button
                          variant="secondary"
                          size="sm"
                          className="h-6 px-2 text-xs"
                          onClick={() => onAddToCompare(property)}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Compare
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Regular Properties */}
        <div>
          <h3 className="text-xl sm:text-2xl font-semibold text-foreground mb-4 sm:mb-6">
            All Properties ({sortedProperties.length} found)
          </h3>
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-card rounded-lg p-4 animate-pulse">
                  <div className="bg-muted h-48 rounded-lg mb-4"></div>
                  <div className="bg-muted h-4 rounded mb-2"></div>
                  <div className="bg-muted h-4 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {regularProperties.map((property) => (
                <div key={property.id} className="relative">
                  <PropertyCard
                    property={property}
                    onViewDetails={onViewDetails}
                  />
                  {onAddToCompare && (
                    <div className="absolute top-2 right-2">
                      {compareProperties.find(p => p.id === property.id) ? (
                        <Badge className="bg-blue-600">
                          ✓ In Compare
                        </Badge>
                      ) : (
                        <Button
                          variant="secondary"
                          size="sm"
                          className="h-6 px-2 text-xs"
                          onClick={() => onAddToCompare(property)}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Compare
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {sortedProperties.length === 0 && !loading && (
          <div className="text-center py-8 sm:py-12">
            <p className="text-muted-foreground text-base sm:text-lg px-4">
              No properties found matching your criteria. Try adjusting your filters.
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default PropertyGrid;