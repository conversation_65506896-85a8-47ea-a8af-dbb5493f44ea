-- ============================================================================
-- ADD ZONE FIELD TO PROPERTIES TABLE MIGRATION
-- ============================================================================
-- This migration adds a zone field to the properties table to allow
-- landlords to specify zones and users to search by zone
-- ============================================================================

-- Add zone field to properties table
ALTER TABLE public.properties 
ADD COLUMN IF NOT EXISTS zone TEXT;

-- Create index on zone field for better search performance
CREATE INDEX IF NOT EXISTS idx_properties_zone ON public.properties(zone);

-- Create index on zone and county combination for efficient filtering
CREATE INDEX IF NOT EXISTS idx_properties_zone_county ON public.properties(zone, county);

-- Create index on zone and available status for search optimization
CREATE INDEX IF NOT EXISTS idx_properties_zone_available ON public.properties(zone, available);

-- Update the updated_at trigger to include zone field changes
-- (This ensures the updated_at timestamp is updated when zone is modified)

-- Create a function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for properties table if it doesn't exist
DROP TRIGGER IF EXISTS update_properties_updated_at ON public.properties;
CREATE TRIGGER update_properties_updated_at
    BEFORE UPDATE ON public.properties
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add some common zones for Kenya (optional - can be customized)
-- These are just examples, landlords can add their own zones
COMMENT ON COLUMN public.properties.zone IS 'Property zone/area within the county (e.g., Westlands, Karen, Kilimani, etc.)';

-- Migration completed successfully!
-- 
-- What was added:
-- - zone TEXT field to properties table
-- - Indexes for efficient zone-based searching
-- - Updated trigger to handle zone field changes
-- 
-- Next steps:
-- 1. Update TypeScript types to include zone field
-- 2. Update application forms to include zone input
-- 3. Update search functionality to filter by zone
