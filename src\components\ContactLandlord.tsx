import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Phone, Mail, MessageCircle, Calendar, Copy, Check } from 'lucide-react';
import { PropertyWithOwner } from '@/types/property';
import { useToast } from '@/hooks/use-toast';

interface ContactLandlordProps {
  property: PropertyWithOwner;
}

export const ContactLandlord: React.FC<ContactLandlordProps> = ({ property }) => {
  const [inquiryType, setInquiryType] = useState('general');
  const [message, setMessage] = useState('');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [preferredContact, setPreferredContact] = useState('phone');
  const [showForm, setShowForm] = useState(false);
  const [copiedPhone, setCopiedPhone] = useState(false);
  const { toast } = useToast();

  const landlordPhone = property.profiles?.phone || "+254 700 000 000";
  const landlordName = property.profiles?.full_name || "Property Owner";

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedPhone(true);
      setTimeout(() => setCopiedPhone(false), 2000);
      toast({
        title: "Phone number copied!",
        description: "You can now paste it in your dialer",
      });
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const getPrefilledMessage = () => {
    const messages = {
      viewing: `Hi ${landlordName},\n\nI'm interested in viewing your property "${property.title}" located at ${property.location}.\n\nI found this listing on BomaHub. Could we schedule a viewing at your convenience?\n\nThank you!`,
      pricing: `Hi ${landlordName},\n\nI'm interested in your property "${property.title}" at ${property.location}.\n\nI found this listing on BomaHub. Could you provide more details about:\n- Final rent amount\n- Security deposit required\n- Any additional fees\n- Move-in requirements\n\nThank you!`,
      availability: `Hi ${landlordName},\n\nI'm interested in your property "${property.title}" at ${property.location}.\n\nI found this listing on BomaHub. Is this property still available? If so, when would be the earliest possible move-in date?\n\nThank you!`,
      general: `Hi ${landlordName},\n\nI'm interested in your property "${property.title}" located at ${property.location}.\n\nI found this listing on BomaHub. Could you please provide more details?\n\nThank you!`
    };
    return messages[inquiryType as keyof typeof messages];
  };

  const handleWhatsAppClick = () => {
    const whatsappNumber = landlordPhone.replace(/\D/g, ''); // Remove non-digits
    const message = encodeURIComponent(getPrefilledMessage());
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleEmailClick = () => {
    const subject = encodeURIComponent(`Inquiry about: ${property.title}`);
    const body = encodeURIComponent(getPrefilledMessage());
    window.open(`mailto:?subject=${subject}&body=${body}`, '_self');
  };

  const handleCallClick = () => {
    window.open(`tel:${landlordPhone}`, '_self');
  };

  const handleSendInquiry = () => {
    // Here you would typically send the inquiry to your backend
    // For now, we'll just compose an email
    const fullMessage = `
Name: ${name}
Email: ${email}
Phone: ${phone}
Preferred Contact: ${preferredContact}
Inquiry Type: ${inquiryType}

Message:
${message}

Property: ${property.title}
Location: ${property.location}
Rent: KSH ${property.rent.toLocaleString()}
    `;

    const subject = encodeURIComponent(`Property Inquiry: ${property.title}`);
    const body = encodeURIComponent(fullMessage);
    window.open(`mailto:?subject=${subject}&body=${body}`, '_self');

    toast({
      title: "Inquiry sent!",
      description: "Your default email client has been opened with the inquiry details.",
    });
  };

  return (
    <Card>
      <CardHeader className="p-4 sm:p-6">
        <CardTitle className="text-lg sm:text-xl">Contact Property Owner</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 p-4 sm:p-6">
        {/* Landlord Info */}
        <div className="bg-muted/30 rounded-lg p-3 sm:p-4">
          <h4 className="font-semibold text-foreground text-sm sm:text-base">{landlordName}</h4>
          <p className="text-xs sm:text-sm text-muted-foreground">Property Owner</p>
          <div className="mt-2 flex items-center gap-2">
            <span className="text-xs sm:text-sm">{landlordPhone}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(landlordPhone)}
              className="h-6 w-6 p-0"
            >
              {copiedPhone ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
            </Button>
          </div>
        </div>

        {/* Quick Contact Buttons */}
        <div className="grid grid-cols-1 gap-3">
          <Button 
            className="w-full justify-start" 
            onClick={handleCallClick}
            size="sm"
          >
            <Phone className="h-4 w-4 mr-2" />
            <span className="text-sm">Call Now</span>
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full justify-start bg-green-50 hover:bg-green-100 border-green-200 dark:bg-green-900/20 dark:hover:bg-green-800/30 dark:border-green-700"
            onClick={handleWhatsAppClick}
            size="sm"
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            <span className="text-sm">WhatsApp Message</span>
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full justify-start"
            onClick={handleEmailClick}
            size="sm"
          >
            <Mail className="h-4 w-4 mr-2" />
            <span className="text-sm">Send Email</span>
          </Button>

          <Button 
            variant="ghost" 
            className="w-full justify-start"
            onClick={() => setShowForm(!showForm)}
            size="sm"
          >
            <Calendar className="h-4 w-4 mr-2" />
            <span className="text-sm">{showForm ? 'Hide' : 'Send'} Detailed Inquiry</span>
          </Button>
        </div>

        {/* Detailed Form */}
        {showForm && (
          <div className="space-y-4 pt-4 border-t">
            <div className="space-y-2">
              <Label className="text-sm">Inquiry Type</Label>
              <Select value={inquiryType} onValueChange={setInquiryType}>
                <SelectTrigger className="text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="viewing">Schedule Viewing</SelectItem>
                  <SelectItem value="pricing">Pricing & Fees</SelectItem>
                  <SelectItem value="availability">Availability</SelectItem>
                  <SelectItem value="general">General Inquiry</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm">Your Name</Label>
                <Input
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Full name"
                  className="text-sm"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm">Your Phone</Label>
                <Input
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="+254 7XX XXX XXX"
                  className="text-sm"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm">Your Email</Label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="text-sm"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm">Preferred Contact Method</Label>
              <Select value={preferredContact} onValueChange={setPreferredContact}>
                <SelectTrigger className="text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="phone">Phone Call</SelectItem>
                  <SelectItem value="whatsapp">WhatsApp</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm">Message</Label>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your message here..."
                rows={4}
                defaultValue={getPrefilledMessage()}
                className="text-sm"
              />
            </div>

            <Button onClick={handleSendInquiry} className="w-full" size="sm">
              <span className="text-sm">Send Inquiry</span>
            </Button>
          </div>
        )}

        {/* Quick Tips */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
          <h5 className="font-medium text-blue-900 dark:text-blue-300 mb-1 text-sm">💡 Contact Tips</h5>
          <ul className="text-xs sm:text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Best calling hours: 9AM - 6PM</li>
            <li>• WhatsApp often gets faster responses</li>
            <li>• Mention you found this on BomaHub</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
